# =============================================================================
# AI TOOLS CRAWLER - COMPLETE SYSTEM CONFIGURATION
# =============================================================================

# =============================================================================
# WEB DASHBOARD SETTINGS
# =============================================================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
FLASK_SECRET_KEY=your-super-secret-key-change-this
DASHBOARD_PORT=5000

# =============================================================================
# CRAWLER SETTINGS
# =============================================================================
CRAWLER_PORT=8000
CRAWLER_API_URL=http://localhost:8000

# =============================================================================
# DATABASE CONFIGURATION (Required)
# =============================================================================

# Option 1: Railway PostgreSQL (Recommended)
# These will be automatically set by Railway when you add PostgreSQL service
DATABASE_URL=postgresql://username:password@host:port/database
PGHOST=your_railway_postgres_host
PGPORT=5432
PGDATABASE=railway
PGUSER=postgres
PGPASSWORD=your_railway_postgres_password

# Option 2: External Supabase (Alternative)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_HOST=db.your-project.supabase.co
SUPABASE_PORT=5432
SUPABASE_DATABASE=postgres
SUPABASE_USER=postgres
SUPABASE_PASSWORD=your_supabase_password

# =============================================================================
# LLM API CONFIGURATION - MODERN & COST-EFFECTIVE MODELS
# =============================================================================

# OpenAI Configuration (Cost-effective models)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000
OPENAI_COST_PER_1K_TOKENS=0.00015

# Anthropic Claude Configuration (Efficient models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-haiku-20240307
ANTHROPIC_MAX_TOKENS=4000
ANTHROPIC_COST_PER_1K_TOKENS=0.00025

# Google Gemini Configuration (Free tier available)
GOOGLE_API_KEY=
GEMINI_API_KEY=
GEMINI_MODEL=gemini-2.0-flash
GEMINI_MAX_TOKENS=4000
GEMINI_COST_PER_1K_TOKENS=0.000075

# Groq Configuration (Fast & Free)
GROQ_API_KEY=
GROQ_MODEL=llama-3.1-70b-versatile
GROQ_MAX_TOKENS=4000
GROQ_COST_PER_1K_TOKENS=0.0

# DeepSeek Configuration (Very cost-effective)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MAX_TOKENS=4000
DEEPSEEK_COST_PER_1K_TOKENS=0.00014

# Qwen Configuration (Alibaba - Cost-effective)
QWEN_API_KEY=your_qwen_api_key_here
QWEN_MODEL=qwen-turbo
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
QWEN_MAX_TOKENS=4000
QWEN_COST_PER_1K_TOKENS=0.0002

# Mistral Configuration (European alternative)
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-small-latest
MISTRAL_BASE_URL=https://api.mistral.ai
MISTRAL_MAX_TOKENS=4000
MISTRAL_COST_PER_1K_TOKENS=0.0002

# Cohere Configuration (Good for embeddings)
COHERE_API_KEY=your_cohere_api_key_here
COHERE_MODEL=command-r
COHERE_MAX_TOKENS=4000
COHERE_COST_PER_1K_TOKENS=0.0005

# Together AI Configuration (Multiple models)
TOGETHER_API_KEY=your_together_api_key_here
TOGETHER_MODEL=meta-llama/Llama-2-70b-chat-hf
TOGETHER_BASE_URL=https://api.together.xyz
TOGETHER_MAX_TOKENS=4000
TOGETHER_COST_PER_1K_TOKENS=0.0009

# Perplexity Configuration (Good for factual info)
PERPLEXITY_API_KEY=your_perplexity_api_key_here
PERPLEXITY_MODEL=llama-3.1-sonar-small-128k-online
PERPLEXITY_BASE_URL=https://api.perplexity.ai
PERPLEXITY_MAX_TOKENS=4000
PERPLEXITY_COST_PER_1K_TOKENS=0.0002

# Ollama Configuration (Local - Free)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b
OLLAMA_COST_PER_1K_TOKENS=0.0

# =================================
# LLM Selection & Fallback - COST-OPTIMIZED
# =================================

# Primary LLM to use (cheapest and most reliable)
PRIMARY_LLM=groq

# Fallback LLMs in order of cost-effectiveness
FALLBACK_LLMS=deepseek,qwen,mistral,gemini,openai,anthropic,cohere,together,perplexity,ollama

# Enable automatic LLM fallback
AUTO_LLM_FALLBACK=true

# LLM timeout in seconds
LLM_TIMEOUT=30

# Cost optimization settings
ENABLE_COST_OPTIMIZATION=true
MAX_COST_PER_REQUEST=0.01
PREFER_FREE_MODELS=true

# Model collaboration settings
ENABLE_MODEL_COLLABORATION=true
COLLABORATION_MODELS=groq,deepseek,gemini
CONSENSUS_THRESHOLD=2

# =================================
# Intelligent Search Configuration
# =================================

# Enable intelligent search features
ENABLE_INTELLIGENT_SEARCH=true

# Search sources priority (1=highest, 5=lowest)
DATABASE_SEARCH_PRIORITY=1
THERESANAIFORTHAT_SEARCH_PRIORITY=2
PRODUCTHUNT_SEARCH_PRIORITY=3
GENERAL_WEB_SEARCH_PRIORITY=4

# Web search engines
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here

# ProductHunt API
PRODUCTHUNT_API_KEY=
# =================================
# Data Enhancement Settings
# =================================

# Enable LLM data enhancement
ENABLE_LLM_ENHANCEMENT=true

# Fields to enhance with LLM
ENHANCE_PROS=true
ENHANCE_CONS=true
ENHANCE_FAQS=true
ENHANCE_DESCRIPTIONS=true
ENHANCE_CATEGORIES=true

# LLM enhancement confidence threshold (0.0-1.0)
LLM_CONFIDENCE_THRESHOLD=0.7

# Maximum enhancement attempts per field
MAX_ENHANCEMENT_ATTEMPTS=3

# =================================
# Search & Crawling Settings
# =================================

# Maximum search results per source
MAX_SEARCH_RESULTS_PER_SOURCE=10

# Search timeout per source (seconds)
SEARCH_TIMEOUT_PER_SOURCE=30

# Enable duplicate detection
ENABLE_DUPLICATE_DETECTION=true

# Similarity threshold for duplicate detection (0.0-1.0)
DUPLICATE_SIMILARITY_THRESHOLD=0.85

# =================================
# Rate Limiting & Performance
# =================================

# Delay between LLM requests (seconds)
LLM_REQUEST_DELAY=1

# Delay between web searches (seconds)
WEB_SEARCH_DELAY=2

# Maximum concurrent searches
MAX_CONCURRENT_SEARCHES=3

# Enable caching
ENABLE_SEARCH_CACHE=true
CACHE_EXPIRY_HOURS=24

# =============================================================================
# RAILWAY-SPECIFIC SETTINGS
# =============================================================================

# Railway Environment
RAILWAY_ENVIRONMENT=production
PORT=8000
PYTHONUNBUFFERED=1

# Logging and Monitoring
LOG_LEVEL=INFO
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_PORT=8000

# =============================================================================
# INSTRUCTIONS FOR RAILWAY DEPLOYMENT
# =============================================================================

# 1. Fork this repository to your GitHub account
# 2. Sign up at railway.app and connect your GitHub
# 3. Create a new project and connect your forked repository
# 4. Add PostgreSQL service to your Railway project
# 5. Copy these environment variables to Railway dashboard
# 6. Update the database values with Railway PostgreSQL credentials
# 7. Add at least one AI API key (GROQ_API_KEY is free and recommended)
# 8. Deploy and monitor logs in Railway dashboard

# =============================================================================
# GETTING API KEYS
# =============================================================================

# GROQ (Free): https://console.groq.com/
# DeepSeek (Cheap): https://platform.deepseek.com/
# Google Gemini (Cheap): https://makersuite.google.com/
# OpenAI: https://platform.openai.com/
# Anthropic: https://console.anthropic.com/
# ProductHunt: https://api.producthunt.com/
