# AI Tools Crawler - Railway Optimized Dependencies
# Minimal set for reliable Railway deployment

# Core web framework - Railway compatible versions
Flask==2.3.3
Flask-SocketIO==5.3.4
Werkzeug==2.3.7
gunicorn==21.2.0
eventlet==0.33.3
schedule==1.2.0
requests==2.31.0
psutil==5.9.5

# Essential dependencies
urllib3>=1.26.0
certifi>=2021.0.0
charset-normalizer>=2.0.0
idna>=2.10
click>=7.0
itsdangerous>=2.0
Jinja2>=3.0
MarkupSafe>=2.0

# Web scraping and parsing
beautifulsoup4==4.12.2
lxml>=5.3.0
html5lib==1.1
requests>=2.31.0

# Browser automation (essential only)
selenium>=4.15.0
webdriver-manager>=4.0.0

# Database connectivity
psycopg2-binary==2.9.9

# Environment and utilities
python-dotenv>=1.0.0
python-dateutil>=2.8.0
pytz>=2023.0
psutil>=5.9.0

# SocketIO dependencies - Railway compatible versions
python-socketio==5.8.0
python-engineio==4.7.1

# Data processing
packaging>=23.0
setuptools>=68.0

# Optional features (commented for faster Railway deployment)
# crawl4ai>=0.6.0  # Enable if needed - large dependency
# playwright>=1.40.0  # Enable if needed - large dependency
# aiohttp>=3.8.0  # Enable if needed
# openai>=1.3.0  # Enable if needed
# google-generativeai>=0.3.0  # Enable if needed
# groq>=0.4.0  # Enable if needed
# google-api-python-client>=2.100.0  # Enable if needed
