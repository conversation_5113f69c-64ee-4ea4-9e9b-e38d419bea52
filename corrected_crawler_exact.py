#!/usr/bin/env python3
"""
🔥 CORRECTED AI TOOLS CRAWLER - EXACT MATCH
Perfect replica of your PostgreSQL tools table structure
Using EMBEDDED CloudflareBypasser with 100% accuracy (hosting-friendly)

🎯 COMPLETE HISTORICAL COVERAGE: 2015-2024 (OLDEST to NEWEST)
📅 Foundation-first crawling building from historical base, complete 10-year coverage
🛡️ Embedded CloudflareBypasser - no external dependencies
"""

import time
import json
import re
import logging
import os
import subprocess
import sys
import sqlite3
from datetime import datetime
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import random

# ===== EMBEDDED CLOUDFLARE BYPASSER =====
# Embedded to avoid hosting issues with git clone
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from DrissionPage import ChromiumPage

class EmbeddedCloudflareBypasser:
    """Embedded CloudflareBypasser to avoid hosting dependencies"""

    def __init__(self, driver: 'ChromiumPage', max_retries=-1, log=True):
        self.driver = driver
        self.max_retries = max_retries
        self.log = log

    def search_recursively_shadow_root_with_iframe(self, ele):
        if ele.shadow_root:
            if ele.shadow_root.child().tag == "iframe":
                return ele.shadow_root.child()
        else:
            for child in ele.children():
                result = self.search_recursively_shadow_root_with_iframe(child)
                if result:
                    return result
        return None

    def search_recursively_shadow_root_with_cf_input(self, ele):
        if ele.shadow_root:
            if ele.shadow_root.ele("tag:input"):
                return ele.shadow_root.ele("tag:input")
        else:
            for child in ele.children():
                result = self.search_recursively_shadow_root_with_cf_input(child)
                if result:
                    return result
        return None

    def is_challenge_present(self):
        """Check if Cloudflare challenge is present"""
        try:
            # Check for common Cloudflare challenge indicators
            page_source = self.driver.html

            # Common Cloudflare challenge indicators
            cf_indicators = [
                "Checking your browser before accessing",
                "DDoS protection by Cloudflare",
                "Please wait while we check your browser",
                "cf-browser-verification",
                "cf-challenge-running",
                "cloudflare",
                "Just a moment",
                "Please enable JavaScript"
            ]

            for indicator in cf_indicators:
                if indicator.lower() in page_source.lower():
                    if self.log:
                        print(f"🛡️ Cloudflare challenge detected: {indicator}")
                    return True

            # Check for challenge forms
            if "challenge-form" in page_source or "cf-challenge" in page_source:
                if self.log:
                    print("🛡️ Cloudflare challenge form detected")
                return True

            return False

        except Exception as e:
            if self.log:
                print(f"⚠️ Error checking for Cloudflare challenge: {e}")
            return False

    def locate_cf_button(self):
        button = None
        eles = self.driver.eles("tag:input")
        for ele in eles:
            if "name" in ele.attrs.keys() and "type" in ele.attrs.keys():
                if "turnstile" in ele.attrs["name"] and ele.attrs["type"] == "hidden":
                    button = ele.parent().shadow_root.child()("tag:body").shadow_root("tag:input")
                    break

        if button:
            return button
        else:
            # If the button is not found, search it recursively
            self.log_message("Basic search failed. Searching for button recursively.")
            ele = self.driver.ele("tag:body")
            iframe = self.search_recursively_shadow_root_with_iframe(ele)
            if iframe:
                button = self.search_recursively_shadow_root_with_cf_input(iframe("tag:body"))
            else:
                self.log_message("Iframe not found. Button search failed.")
            return button

    def log_message(self, message):
        if self.log:
            print(f"🛡️ CloudflareBypasser: {message}")

    def click_verification_button(self):
        try:
            button = self.locate_cf_button()
            if button:
                self.log_message("Verification button found. Attempting to click.")
                button.click()
            else:
                self.log_message("Verification button not found.")
        except Exception as e:
            self.log_message(f"Error clicking verification button: {e}")

    def is_bypassed(self):
        try:
            title = self.driver.title.lower()
            return "just a moment" not in title
        except Exception as e:
            self.log_message(f"Error checking page title: {e}")
            return False

    def bypass(self):

        try_count = 0

        while not self.is_bypassed():
            if 0 < self.max_retries + 1 <= try_count:
                self.log_message("Exceeded maximum retries. Bypass failed.")
                break

            self.log_message(f"Attempt {try_count + 1}: Verification page detected. Trying to bypass...")
            self.click_verification_button()

            try_count += 1
            time.sleep(2)

        if self.is_bypassed():
            self.log_message("Bypass successful.")
        else:
            self.log_message("Bypass failed.")
# ===== END EMBEDDED CLOUDFLARE BYPASSER =====

def smart_cloudflare_detection(driver, logger=None):
    """
    Smart Cloudflare detection that checks multiple factors to avoid false positives
    Returns: (is_cloudflare, confidence_level, reason)
    """
    try:
        # Get page information
        title = driver.title.lower()
        html_content = driver.html.lower()
        current_url = driver.url.lower()

        # Strong indicators of Cloudflare protection (high confidence)
        strong_indicators = [
            "just a moment",
            "checking your browser",
            "please wait while we",
            "cloudflare-browser-check",
            "cf-browser-verification"
        ]

        # Check for strong indicators
        strong_match = any(indicator in title or indicator in html_content for indicator in strong_indicators)

        if strong_match:
            reason = "Strong Cloudflare indicators detected"
            if logger:
                logger.info(f"🛡️ {reason}")
            return True, "high", reason

        # Check for actual content indicators (page is loaded)
        content_indicators = [
            len(html_content) > 50000,  # Substantial content
            "theresanaiforthat.com" in current_url and ("ai tool" in html_content or "discover" in html_content),
            any(word in title for word in ["ai", "tool", "discover", "period"]),
            "<main" in html_content or "<article" in html_content or "class=" in html_content
        ]

        content_score = sum(content_indicators)

        # If we have good content indicators, likely not Cloudflare
        if content_score >= 2:
            reason = f"Page appears loaded (content score: {content_score}/4)"
            if logger:
                logger.info(f"✅ {reason}")
            return False, "high", reason

        # Weak indicators that might be false positives
        weak_indicators = [
            "cloudflare" in html_content,  # Just mentioning cloudflare
            "ray id" in html_content and len(html_content) < 10000  # Small page with ray id
        ]

        weak_match = any(indicator for indicator in weak_indicators)

        if weak_match and content_score == 0:
            reason = "Weak Cloudflare indicators with no content"
            if logger:
                logger.warning(f"⚠️ {reason}")
            return True, "low", reason

        # Default: no Cloudflare detected
        reason = f"No Cloudflare detected (content score: {content_score}/4)"
        if logger:
            logger.info(f"✅ {reason}")
        return False, "high", reason

    except Exception as e:
        if logger:
            logger.warning(f"⚠️ Error in smart Cloudflare detection: {e}")
        return False, "low", f"Detection error: {e}"

import requests
from urllib.parse import urlparse, urljoin

# Try to import PostgreSQL libraries
try:
    import psycopg2
    import psycopg2.extras
    POSTGRES_AVAILABLE = True
except ImportError:
    try:
        import psycopg2
        import psycopg2.extras
        POSTGRES_AVAILABLE = True
    except ImportError:
        print("⚠️ psycopg2 not available, will use requests for Supabase API")
        POSTGRES_AVAILABLE = False

try:
    from dotenv import load_dotenv
    load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    print("⚠️ python-dotenv not available, using os.environ")
    DOTENV_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def safe_print(text):
    """Print text safely, handling Unicode encoding errors"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Remove emojis and special characters for Windows compatibility
        import re
        clean_text = re.sub(r'[^\x00-\x7F]+', '', text)
        print(clean_text)

class CorrectedExactCrawler:
    """Corrected crawler matching your exact PostgreSQL table structure"""

    def __init__(self):
        self.base_url = "https://theresanaiforthat.com"
        self.extracted_tools = []
        self.success_count = 0
        self.error_count = 0
        self.blocked_count = 0  # Counter for blocked content
        self.incomplete_count = 0  # Counter for incomplete data
        self.batch_id = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Database configuration - Supabase (direct to main table)
        self.database_config = self.setup_database_config()

        # Content filtering - Blocked categories (specific dating/romance phrases)
        self.blocked_categories = {
            'virtual girlfriend', 'ex-girlfriend simulation', 'anime girlfriend ais',
            'dating advice', 'online dating chat suggestions', 'online dating assistance',
            'dating profile optimization', 'dating profile bios', 'dating simulator',
            'dating profile images', 'dating communication', 'dating conversations',
            'dating profiles', 'virtual dating rpg', 'alien dating profiles',
            'virtual dating game', 'anime dating simulation', 'mindful dating',
            'virtual dating ais', 'girlfriend ai', 'virtual girlfriend ai',
            'romance ai', 'romantic ai', 'flirt ai', 'dating ai',
            'virtual companion ai', 'ai girlfriend', 'ai boyfriend',
            'dating app', 'dating site', 'dating coach', 'dating tips',
            'love letter', 'love poem', 'love song', 'love story', 'love advice',
            'romance novel', 'romance story', 'romance game', 'romantic relationship',
            'relationship advice', 'relationship coach', 'relationship tips',
            'virtual companion for dating', 'ai companion for dating', 'dating companion',
            'girlfriend simulator', 'boyfriend simulator', 'romantic companion',
            'nsfw', 'adult content', 'erotic', 'sexual', 'porn', 'xxx',
            'hookup', 'flirting', 'seduction', 'sensual',
            'intimate relationship', 'intimate chat', 'intimate conversation',
            'virtual companion', 'ai companion', 'romantic companion'
        }

        # Single dangerous words that should be blocked in any context
        self.dangerous_single_words = {
            'girlfriend', 'boyfriend', 'dating', 'romance', 'romantic', 'love', 'flirt', 'flirting',
            'companionship'
        }

        # Primary task specific blocks
        self.blocked_primary_tasks = {
            'online dating assistance', 'online dating chat suggestions', 'dating advice',
            'companionship', 'virtual girlfriend', 'love letters', 'flirting',
            'dating assistance', 'love compatibility'
        }

        # Required fields for data quality
        self.required_fields = [
            'logo_url', 'company_name', 'short_description',
            'full_description', 'primary_task', 'visit_website_url'
        ]

        # Intelligent Search Configuration
        self.enable_intelligent_search = os.getenv('ENABLE_INTELLIGENT_SEARCH', 'false').lower() == 'true'
        self.enable_llm_enhancement = os.getenv('ENABLE_LLM_ENHANCEMENT', 'false').lower() == 'true'
        self.primary_llm = os.getenv('PRIMARY_LLM', 'groq')
        self.fallback_llms = os.getenv('FALLBACK_LLMS', 'openai,anthropic,google').split(',')
        self.llm_timeout = int(os.getenv('LLM_TIMEOUT', '30'))

        # LLM API keys - MODERN & COST-EFFECTIVE MODELS
        self.llm_keys = {
            'groq': os.getenv('GROQ_API_KEY'),
            'deepseek': os.getenv('DEEPSEEK_API_KEY'),
            'qwen': os.getenv('QWEN_API_KEY'),
            'mistral': os.getenv('MISTRAL_API_KEY'),
            'gemini': os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY'),
            'openai': os.getenv('OPENAI_API_KEY'),
            'anthropic': os.getenv('ANTHROPIC_API_KEY'),
            'cohere': os.getenv('COHERE_API_KEY'),
            'together': os.getenv('TOGETHER_API_KEY'),
            'perplexity': os.getenv('PERPLEXITY_API_KEY'),
            'huggingface': os.getenv('HUGGINGFACE_API_KEY')
        }

        # Model costs per 1K tokens (for optimization)
        self.model_costs = {
            'groq': 0.0,  # Free
            'deepseek': 0.00014,  # Very cheap
            'qwen': 0.0002,  # Cheap
            'mistral': 0.0002,  # Cheap
            'gemini': 0.000075,  # Very cheap
            'openai': 0.00015,  # Moderate
            'anthropic': 0.00025,  # Moderate
            'cohere': 0.0005,  # Higher
            'together': 0.0009,  # Higher
            'perplexity': 0.0002,  # Cheap
            'ollama': 0.0  # Free (local)
        }

        # Cost optimization settings
        self.enable_cost_optimization = os.getenv('ENABLE_COST_OPTIMIZATION', 'true').lower() == 'true'
        self.max_cost_per_request = float(os.getenv('MAX_COST_PER_REQUEST', '0.01'))
        self.prefer_free_models = os.getenv('PREFER_FREE_MODELS', 'true').lower() == 'true'

        # Model collaboration settings
        self.enable_model_collaboration = os.getenv('ENABLE_MODEL_COLLABORATION', 'true').lower() == 'true'
        self.collaboration_models = os.getenv('COLLABORATION_MODELS', 'groq,deepseek,gemini').split(',')
        self.consensus_threshold = int(os.getenv('CONSENSUS_THRESHOLD', '2'))

        # Search API keys
        self.google_search_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.google_search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.bing_search_key = os.getenv('BING_SEARCH_API_KEY')
        self.producthunt_key = os.getenv('PRODUCTHUNT_API_KEY')

        # Search settings
        self.max_search_results = int(os.getenv('MAX_SEARCH_RESULTS_PER_SOURCE', '10'))
        self.search_timeout = int(os.getenv('SEARCH_TIMEOUT_PER_SOURCE', '30'))
        self.enable_duplicate_detection = os.getenv('ENABLE_DUPLICATE_DETECTION', 'true').lower() == 'true'
        self.duplicate_threshold = float(os.getenv('DUPLICATE_SIMILARITY_THRESHOLD', '0.85'))
        
        # Period URLs - CORRECT URLS from OLDEST to NEWEST for foundation-first coverage
        self.period_urls = [
            # Start from the OLDEST available periods
            "https://theresanaiforthat.com/period/2015/",
            "https://theresanaiforthat.com/period/2016/",
            "https://theresanaiforthat.com/period/2017/",
            "https://theresanaiforthat.com/period/2018/",
            "https://theresanaiforthat.com/period/2019/",
            "https://theresanaiforthat.com/period/2020/",
            "https://theresanaiforthat.com/period/2021/",
            "https://theresanaiforthat.com/period/2022/",
            "https://theresanaiforthat.com/period/2023/",
            "https://theresanaiforthat.com/period/2024/",

            # Recent periods (monthly)
            "https://theresanaiforthat.com/period/january/",
            "https://theresanaiforthat.com/period/february/",
            "https://theresanaiforthat.com/period/march/",
            "https://theresanaiforthat.com/period/april/",
            "https://theresanaiforthat.com/period/may/",
            "https://theresanaiforthat.com/period/june/",
            "https://theresanaiforthat.com/period/july/",
            "https://theresanaiforthat.com/period/august/",
            "https://theresanaiforthat.com/period/september/",
            "https://theresanaiforthat.com/period/october/",
            "https://theresanaiforthat.com/period/november/",
            "https://theresanaiforthat.com/period/december/",

            # Latest releases
            "https://theresanaiforthat.com/period/just-released/"
        ]

        # AI tools list - will be populated dynamically from period pages
        # For testing, add some sample tools
        self.ai_tools_list = [
            "triviat", "wave", "korbit", "fastwrite", "chatgpt",
            "claude", "midjourney", "stable-diffusion", "copilot", "gemini"
        ]
        self.discovered_tools = set()  # To avoid duplicates
        
        # Setup
        self.setup_database()
        # Skip creating unwanted directories to prevent file clutter

        # Progress tracking (in memory only to prevent unwanted files)
        self.progress_file = None  # Disabled to prevent unwanted files
        self.processed_tools = set()
        self.current_period_index = 0
        self.load_progress()

        # Browser cleanup tracking
        self.driver = None
        self.cf_bypasser = None
        self._cleanup_registered = False

        # Register cleanup handlers
        self.register_cleanup_handlers()

        # Clean up any existing debug files
        self.cleanup_debug_files()

        safe_print("🔥 AI TOOLS CRAWLER")
        safe_print("=" * 60)
        safe_print("💻 Running locally")
        safe_print(f"🔗 Database: {self.database_config.get('source', 'unknown').title()}")
        safe_print("✅ Exact match for PostgreSQL tools table")
        safe_print("✅ JSONB arrays for applicable_tasks, pros, cons")
        safe_print("✅ Proper data types and constraints")
        safe_print("✅ 100% Embedded CloudflareBypasser (hosting-friendly)")
        if self.enable_intelligent_search:
            safe_print("🔍 Intelligent multi-source search enabled")
            available_llms = [name for name, key in self.llm_keys.items() if key]
            if available_llms:
                safe_print(f"🤖 LLM enhancement available: {', '.join(available_llms)}")
            else:
                safe_print("⚠️ No LLM API keys configured")
        else:
            safe_print("⚠️ Intelligent search disabled (set ENABLE_INTELLIGENT_SEARCH=true)")
        safe_print("=" * 60)



    def setup_database_config(self):
        """Setup database configuration for Supabase"""
        # Check for Supabase configuration
        supabase_host = os.getenv('SUPABASE_HOST')
        if supabase_host:
            logger.info("🔗 Using Supabase PostgreSQL database")
            return {
                'host': supabase_host,
                'port': os.getenv('SUPABASE_PORT', '5432'),
                'database': os.getenv('SUPABASE_DATABASE', 'postgres'),
                'user': os.getenv('SUPABASE_USER'),
                'password': os.getenv('SUPABASE_PASSWORD'),
                'source': 'supabase'
            }

        logger.error("❌ No database configuration found!")
        logger.error("Please set Supabase credentials")
        return None

    def register_cleanup_handlers(self):
        """Register cleanup handlers for proper resource management"""
        if not self._cleanup_registered:
            import atexit
            import signal

            # Register cleanup on normal exit
            atexit.register(self.cleanup_resources)

            # Register cleanup on signal termination
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            self._cleanup_registered = True
            logger.info("✅ Cleanup handlers registered")

    def _signal_handler(self, signum, frame):
        """Handle termination signals"""
        logger.info(f"🛑 Received signal {signum}, cleaning up...")
        self.cleanup_resources()
        exit(0)

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup_resources()

    def run_period_specific_crawl(self, year: str, months_range: tuple):
        """Run crawl for specific months within a year using EXACT same method as option 1"""
        start_month, end_month = months_range
        logger.info(f"🗓️ PERIOD SPECIFIC CRAWL: {year} months {start_month}-{end_month}")
        logger.info("=" * 80)

        # Install dependencies first (SAME AS OPTION 1)
        if not self.install_dependencies():
            logger.error("❌ Failed to install dependencies")
            return False

        # Setup browser (SAME AS OPTION 1)
        if not self.setup_browser():
            logger.error("❌ Failed to setup browser")
            return False

        try:
            total_extracted = 0
            month_names = ["", "january", "february", "march", "april", "may", "june",
                          "july", "august", "september", "october", "november", "december"]

            for month_num in range(start_month, end_month + 1):
                month_name = month_names[month_num]
                logger.info(f"📅 Processing {year} {month_name.title()}")

                # Use the monthly crawl method for each month
                logger.info(f"🔄 Running monthly crawl for {month_name} {year}")
                month_extracted = self.run_single_month_extraction(year, month_num)
                total_extracted += month_extracted

                logger.info(f"✅ Completed {month_name} {year}: {month_extracted} tools extracted")

            logger.info(f"🎉 Specific period crawl completed! Total extracted: {total_extracted}")

        except Exception as e:
            logger.error(f"❌ Error in specific period crawl: {e}")
            traceback.print_exc()
        finally:
            self.cleanup_resources()

    def run_monthly_crawl(self, year: str, month: int):
        """Run crawl for a specific month using EXACT same method as option 1"""
        month_names = ["", "january", "february", "march", "april", "may", "june",
                      "july", "august", "september", "october", "november", "december"]
        month_name = month_names[month]

        logger.info(f"🗓️ MONTHLY CRAWL: {year} {month_name.title()}")
        logger.info("=" * 80)

        # Create a temporary period URL for this month
        month_url = f"https://theresanaiforthat.com/period/{year}/{month_name}/"

        # Set up the period URLs to only include this month
        original_period_urls = self.period_urls
        self.period_urls = [month_url]

        logger.info(f"🎯 Targeting single month: {month_url}")

        try:
            # Use the EXACT same method as option 1 (run_comprehensive_crawl)
            result = self.run_comprehensive_crawl(discovery_mode=True)
            return result
        finally:
            # Restore original period URLs
            self.period_urls = original_period_urls


                # Use the EXACT same method as option 1 for bypassing and extracting
                try:
                    # Use the SAME powerful method as option 1 for page loading
                    logger.info(f"🌐 Loading period page: {url_pattern}")

                    # Add random delay to appear more human-like
                    import random
                    random_delay = random.uniform(3, 7)
                    logger.info(f"⏳ Random delay: {random_delay:.1f} seconds")
                    time.sleep(random_delay)

                    self.driver.get(url_pattern)
                    time.sleep(8)

                    # Use smart Cloudflare detection like option 1
                    page_title = self.driver.title
                    logger.info(f"📄 Page title: {page_title}")

                    # Smart Cloudflare detection (SAME AS OPTION 1)
                    is_cloudflare, confidence, reason = smart_cloudflare_detection(self.driver, logger)

                    if is_cloudflare and confidence == "high":
                        logger.info(f"🛡️ Cloudflare detected: {reason}")
                        try:
                            # Try multiple bypass strategies
                            bypass_success = False

                            # Strategy 1: Quick bypass attempt
                            logger.info("🔧 Strategy 1: Quick bypass attempt...")
                            try:
                                self.cf_bypasser.bypass()
                                time.sleep(5)

                                # Check if bypass worked
                                new_title = self.driver.title.lower()
                                if "just a moment" not in new_title:
                                    logger.info("✅ Quick bypass successful!")
                                    bypass_success = True
                                else:
                                    logger.info("⚠️ Quick bypass failed, trying extended method...")
                            except Exception as e:
                                logger.warning(f"⚠️ Quick bypass error: {e}")

                            # Strategy 2: Extended bypass with timeout if quick failed
                            if not bypass_success:
                                logger.info("🔧 Strategy 2: Extended bypass with timeout...")
                                import threading

                                bypass_completed = False
                                bypass_error = None

                                def run_extended_bypass():
                                    nonlocal bypass_completed, bypass_error
                                    try:
                                        # Wait a bit then try again
                                        time.sleep(10)
                                        self.cf_bypasser.bypass()
                                        bypass_completed = True
                                    except Exception as e:
                                        bypass_error = e

                                bypass_thread = threading.Thread(target=run_extended_bypass)
                                bypass_thread.daemon = True
                                bypass_thread.start()
                                bypass_thread.join(timeout=60)  # Reduced timeout

                                if bypass_completed:
                                    logger.info("✅ Extended bypass successful")
                                    bypass_success = True
                                    time.sleep(10)
                                else:
                                    logger.warning("⚠️ Extended bypass failed or timed out")

                            # Strategy 3: Try alternative URL approach if all failed
                            if not bypass_success:
                                logger.info("🔧 Strategy 3: Trying alternative URL approach...")
                                try:
                                    # Try accessing the main period page first
                                    main_period_url = "https://theresanaiforthat.com/period/"
                                    logger.info(f"🌐 Trying main period page: {main_period_url}")
                                    self.driver.get(main_period_url)
                                    time.sleep(10)

                                    # Then navigate to specific month
                                    logger.info(f"🌐 Now navigating to specific month: {url_pattern}")
                                    self.driver.get(url_pattern)
                                    time.sleep(15)

                                    # Check if this worked
                                    final_title = self.driver.title.lower()
                                    if "just a moment" not in final_title:
                                        logger.info("✅ Alternative URL approach successful!")
                                        bypass_success = True
                                    else:
                                        logger.warning("⚠️ Alternative approach also failed")
                                except Exception as e:
                                    logger.warning(f"⚠️ Alternative approach error: {e}")

                            if not bypass_success:
                                logger.warning("⚠️ All bypass strategies failed, skipping this URL pattern")
                                continue

                        except Exception as e:
                            logger.warning(f"⚠️ Bypass error: {e}")
                            continue
                    elif is_cloudflare and confidence == "low":
                        logger.info("⚠️ Low confidence Cloudflare detection, continuing...")
                        time.sleep(5)
                    else:
                        logger.info("✅ No Cloudflare protection detected")

                    # Check for pagination and extract tools (SAME AS OPTION 1)
                    page = 1
                    while True:
                        if page > 1:
                            paginated_url = f"{url_pattern}page/{page}/"
                            logger.info(f"🔍 Checking page {page}: {paginated_url}")
                            page_success = self.bypass_and_load_url(paginated_url)
                            if not page_success:
                                break

                        # Extract tools from current page using EXACT same method
                        page_tools = self.extract_tools_from_page()

                        if not page_tools:
                            if page == 1:
                                logger.warning(f"⚠️ No tools found on first page for {month_name} {year}")
                            break

                        tools_found = True
                        logger.info(f"✅ Found {len(page_tools)} tools on page {page} for {month_name} {year}")

                        # Process each tool using EXACT same method as option 1
                        for tool_slug in page_tools:
                            tool_url = f"{self.base_url}/ai/{tool_slug}/"
                            # Use the SAME powerful bypass method as option 1
                            tool_data = self.bypass_and_extract_with_retries(tool_url)

                            if tool_data:
                                # Apply content filtering (SAME AS OPTION 1)
                                if self.is_content_blocked(tool_data):
                                    logger.warning(f"🚫 Content blocked: {tool_slug}")
                                    continue

                                # Save to database (SAME AS OPTION 1)
                                if self.save_to_database(tool_data):
                                    total_extracted += 1
                                    logger.info(f"✅ Extracted: {tool_slug}")
                                else:
                                    logger.error(f"❌ Failed to save: {tool_slug}")

                            # Delay between tools (SAME AS OPTION 1)
                            time.sleep(5)

                        page += 1

                        # Safety limit for pagination
                        if page > 50:
                            logger.warning(f"⚠️ Reached pagination limit (50 pages) for {month_name} {year}")
                            break

                    if tools_found:
                        break

                except Exception as url_error:
                    logger.error(f"❌ Error with URL {url_pattern}: {url_error}")
                    continue

            if not tools_found:
                logger.warning(f"⚠️ No tools found for {month_name} {year} in any URL pattern")

            logger.info(f"🎉 Monthly crawl completed! Total extracted: {total_extracted}")

        except Exception as e:
            logger.error(f"❌ Error in monthly crawl: {e}")
            traceback.print_exc()
        finally:
            self.cleanup_resources()

    def discover_tools_from_current_page(self):
        """Discover tools from the currently loaded page"""
        try:
            html_content = self.driver.html
            soup = BeautifulSoup(html_content, 'html.parser')

            tools = []

            # Method 1: Look for links to /ai/ pages
            ai_links = soup.find_all('a', href=re.compile(r'/ai/[^/]+/?$'))
            for link in ai_links:
                href = link.get('href', '')
                slug_match = re.search(r'/ai/([^/]+)/?$', href)
                if slug_match:
                    slug = slug_match.group(1)
                    if slug not in ['', 'index', 'home'] and slug not in tools:
                        tools.append(slug)

            # Method 2: Look for tool cards or listings
            tool_cards = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|item'))
            for card in tool_cards:
                link = card.find('a', href=re.compile(r'/ai/[^/]+/?$'))
                if link:
                    href = link.get('href', '')
                    slug_match = re.search(r'/ai/([^/]+)/?$', href)
                    if slug_match:
                        slug = slug_match.group(1)
                        if slug not in ['', 'index', 'home'] and slug not in tools:
                            tools.append(slug)

            logger.info(f"🔍 Discovered {len(tools)} tools from current page")
            return tools

        except Exception as e:
            logger.error(f"❌ Error discovering tools from current page: {e}")
            return []

    def bypass_cloudflare_and_load(self, url: str) -> bool:
        """Load a URL with Cloudflare bypass"""
        try:
            logger.info(f"🔍 Loading URL: {url}")

            # Navigate to URL
            self.driver.get(url)
            time.sleep(3)

            # Check if Cloudflare challenge is present
            try:
                if self.cf_bypasser and self.cf_bypasser.is_challenge_present():
                    logger.info("🛡️ Cloudflare challenge detected, bypassing...")
                    success = self.cf_bypasser.bypass()
                    if not success:
                        logger.error("❌ Failed to bypass Cloudflare")
                        return False
                    logger.info("✅ Cloudflare bypass successful")
                else:
                    logger.info("✅ No Cloudflare challenge detected")
            except Exception as cf_error:
                logger.warning(f"⚠️ Cloudflare bypass error: {cf_error}")
                # Continue anyway, maybe the page loaded fine

            # Wait for page to load
            time.sleep(2)

            # Check if page loaded successfully
            html_content = self.driver.html
            if len(html_content) < 1000:
                logger.warning(f"⚠️ Page content too short ({len(html_content)} chars)")
                return False

            # Check for error indicators
            if "404" in html_content or "not found" in html_content.lower():
                logger.warning("⚠️ Page not found (404)")
                return False

            logger.info("✅ Page loaded successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error loading URL {url}: {e}")
            return False

    def bypass_and_load_url(self, url: str) -> bool:
        """Load URL using EXACT same method as option 1 - with proper Cloudflare bypass"""
        try:
            logger.info(f"🔍 Loading URL (Option 1 method): {url}")

            # Navigate to URL
            self.driver.get(url)
            time.sleep(5)

            # Use the same bypass logic as option 1
            if self.cf_bypasser:
                try:
                    # Check if bypass is needed
                    if not self.cf_bypasser.is_bypassed():
                        logger.info("🛡️ Attempting Cloudflare bypass...")
                        bypass_success = self.cf_bypasser.bypass()
                        if bypass_success:
                            logger.info("✅ Cloudflare bypass successful")
                            # Give extra time after successful bypass
                            time.sleep(5)
                        else:
                            logger.warning("⚠️ Cloudflare bypass failed, but continuing...")
                            # Don't return False immediately, try to continue
                    else:
                        logger.info("✅ Already bypassed Cloudflare")
                except Exception as cf_error:
                    logger.warning(f"⚠️ Cloudflare bypass error: {cf_error}, but continuing...")
                    # Don't return False immediately, try to continue

            # Wait for page to stabilize
            time.sleep(3)

            # Verify page loaded successfully
            html_content = self.driver.html
            logger.info(f"📄 Page content length: {len(html_content)} characters")

            if len(html_content) < 1000:
                logger.warning(f"⚠️ Page content too short ({len(html_content)} chars)")
                return False

            # Check for common error indicators
            if any(indicator in html_content.lower() for indicator in ["404", "not found", "page not found"]):
                logger.warning("⚠️ Page not found (404)")
                return False

            # Check if still showing Cloudflare challenge (but don't fail immediately)
            if any(indicator in html_content.lower() for indicator in ["just a moment", "checking your browser"]):
                logger.warning("⚠️ Still showing Cloudflare challenge, but page has content")
                # Continue anyway if page has substantial content

            # Check for actual content indicators
            if any(indicator in html_content.lower() for indicator in ["ai tool", "artificial intelligence", "period", "tools"]):
                logger.info("✅ Page contains expected AI tools content")
                return True

            logger.info("✅ Page loaded successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error loading URL {url}: {e}")
            return False

    def cleanup_resources(self):
        """Comprehensive cleanup of all resources"""
        try:
            logger.info("🧹 Starting resource cleanup...")

            # Close browser driver
            if hasattr(self, 'driver') and self.driver:
                try:
                    logger.info("🌐 Closing browser...")
                    self.driver.quit()
                    self.driver = None
                    logger.info("✅ Browser closed successfully")
                except Exception as e:
                    logger.warning(f"⚠️ Error closing browser: {e}")
                    try:
                        # Force kill browser processes
                        import psutil
                        for proc in psutil.process_iter(['pid', 'name']):
                            if 'chrome' in proc.info['name'].lower() or 'chromium' in proc.info['name'].lower():
                                proc.kill()
                        logger.info("✅ Browser processes force killed")
                    except:
                        pass

            # Clear CloudflareBypasser
            if hasattr(self, 'cf_bypasser') and self.cf_bypasser:
                try:
                    self.cf_bypasser = None
                    logger.info("✅ CloudflareBypasser cleared")
                except Exception as e:
                    logger.warning(f"⚠️ Error clearing CloudflareBypasser: {e}")

            logger.info("✅ Resource cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

    def cleanup_debug_files(self):
        """Clean up any existing debug files to prevent project bloat"""
        try:
            import glob

            # Patterns for debug files
            debug_patterns = [
                'debug_*.html',
                'debug_*.txt',
                'debug_*.json',
                '*_debug.*',
                'debug_extraction_*',
                'debug_html_*',
                'debug_no_tools_*'
            ]

            cleaned_count = 0
            for pattern in debug_patterns:
                debug_files = glob.glob(pattern)
                for debug_file in debug_files:
                    try:
                        os.remove(debug_file)
                        cleaned_count += 1
                        logger.debug(f"🗑️ Removed debug file: {debug_file}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not remove {debug_file}: {e}")

            if cleaned_count > 0:
                logger.info(f"🧹 Cleaned up {cleaned_count} debug files")
            else:
                logger.debug("✅ No debug files found to clean")

        except Exception as e:
            logger.warning(f"⚠️ Debug cleanup failed: {e}")

    def setup_database(self):
        """Setup database connection and test"""
        try:
            # Test Supabase connection if configured
            if self.database_config and self.database_config.get('source') == 'supabase':
                import psycopg2
                from psycopg2.extras import RealDictCursor

                conn = psycopg2.connect(
                    host=self.database_config['host'],
                    port=self.database_config['port'],
                    database=self.database_config['database'],
                    user=self.database_config['user'],
                    password=self.database_config['password'],
                    cursor_factory=RealDictCursor
                )

                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM tools")
                count = cursor.fetchone()['count']
                logger.info(f"✅ Database connected successfully - Current records: {count}")

                # Test write
                test_slug = f'test-tool-{int(time.time())}'
                cursor.execute("""
                    INSERT INTO tools (company_name, slug, created_at, updated_at)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (slug) DO NOTHING
                """, ('TEST_TOOL', test_slug, datetime.now(), datetime.now()))

                cursor.execute("DELETE FROM tools WHERE company_name = 'TEST_TOOL'")
                conn.commit()
                conn.close()

                logger.info("✅ Database write test successful: TEST_TOOL")
                logger.info("✅ Database setup completed - EXACT PostgreSQL replica")
                return True

            # Fallback to SQLite setup
            # Delete corrupted database file if it exists
            db_file = 'ai_tools_exact.db'
            if os.path.exists(db_file):
                try:
                    # Try to connect and test with timeout
                    test_conn = sqlite3.connect(db_file, timeout=10.0)
                    test_cursor = test_conn.cursor()
                    test_cursor.execute("SELECT COUNT(*) FROM sqlite_master")
                    test_conn.close()
                    logger.info("✅ Database file is valid")
                except (sqlite3.DatabaseError, sqlite3.OperationalError) as e:
                    logger.warning(f"⚠️ Database file issue: {e}")
                    logger.warning("⚠️ Deleting and recreating database...")
                    try:
                        os.remove(db_file)
                    except:
                        pass

            # Connect with timeout to prevent locking issues
            self.conn = sqlite3.connect(db_file, timeout=60.0)
            cursor = self.conn.cursor()

            # Enable WAL mode to reduce locking issues
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.execute("PRAGMA cache_size=10000")
            cursor.execute("PRAGMA temp_store=memory")

            # Drop existing table to recreate with exact structure
            cursor.execute("DROP TABLE IF EXISTS tools")

            # Create EXACT replica of PostgreSQL tools table (direct extraction)
            cursor.execute("""
                CREATE TABLE tools (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    logo_url TEXT NULL,
                    company_name TEXT NULL,
                    short_description TEXT NULL,
                    full_description TEXT NULL,
                    primary_task TEXT NULL,
                    applicable_tasks TEXT NULL,  -- JSON string for JSONB array
                    pros TEXT NULL,              -- JSON string for JSONB array
                    cons TEXT NULL,              -- JSON string for JSONB array
                    pricing TEXT NULL,
                    featured_image_url TEXT NULL,
                    visit_website_url TEXT NULL,
                    faqs TEXT NULL,              -- JSON string for JSONB
                    click_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    slug TEXT UNIQUE,
                    detail_url TEXT NULL,
                    is_featured BOOLEAN DEFAULT 0,
                    is_verified BOOLEAN DEFAULT 0,
                    crawl_source TEXT DEFAULT 'theresanaiforthat.com',
                    crawl_batch_id TEXT,
                    crawl_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data_quality_score INTEGER DEFAULT 0,
                    extraction_success BOOLEAN DEFAULT 1,
                    validation_notes TEXT NULL
                )
            """)

            # Create all indexes exactly like PostgreSQL
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tools_slug ON tools(slug)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tools_crawl_batch ON tools(crawl_batch_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tools_created_at ON tools(created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tools_data_quality ON tools(data_quality_score)")

            conn.commit()

            # TEST DATABASE CONNECTION
            cursor.execute("SELECT COUNT(*) FROM tools")
            count = cursor.fetchone()[0]
            logger.info(f"✅ Database connected successfully - Current records: {count}")

            # TEST INSERT
            test_data = {
                'company_name': 'TEST_TOOL',
                'slug': 'test-connection',
                'short_description': 'Test connection',
                'full_description': 'Testing database connection',
                'primary_task': 'Testing',
                'pricing': 'free',
                'crawl_batch_id': self.batch_id,
                'data_quality_score': 100
            }

            cursor.execute("""
                INSERT OR REPLACE INTO tools (
                    company_name, slug, short_description, full_description,
                    primary_task, pricing, crawl_batch_id, data_quality_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_data['company_name'], test_data['slug'], test_data['short_description'],
                test_data['full_description'], test_data['primary_task'], test_data['pricing'],
                test_data['crawl_batch_id'], test_data['data_quality_score']
            ))

            conn.commit()

            # Verify test insert
            cursor.execute("SELECT company_name FROM tools WHERE slug = ?", (test_data['slug'],))
            result = cursor.fetchone()
            if result:
                logger.info(f"✅ Database write test successful: {result[0]}")
                # Clean up test record
                cursor.execute("DELETE FROM tools WHERE slug = ?", (test_data['slug'],))
                conn.commit()
            else:
                logger.error("❌ Database write test failed!")

            conn.close()
            logger.info("✅ Database setup completed - EXACT PostgreSQL replica")
            return True

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                logger.warning(f"⚠️ Database is locked, trying to resolve...")
                # Wait and try again
                time.sleep(5)
                try:
                    # Force close any existing connections
                    if 'conn' in locals():
                        conn.close()

                    # Try again with a new connection
                    conn = sqlite3.connect(db_file, timeout=60.0)
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA journal_mode=WAL")
                    logger.info("✅ Database lock resolved")
                    conn.close()
                    return True
                except Exception as retry_error:
                    logger.error(f"❌ Could not resolve database lock: {retry_error}")
                    return False
            else:
                logger.error(f"❌ Database operation failed: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def install_dependencies(self):
        """Install dependencies and setup CloudflareBypasser"""
        logger.info("Installing dependencies...")

        try:
            # CloudflareBypasser is now embedded - no external clone needed
            logger.info("✅ CloudflareBypasser embedded - no external dependencies needed")

            # Skip automatic package installation - assume packages are pre-installed
            logger.info("✅ Skipping automatic package installation - using pre-installed packages")

            # Skip PostgreSQL package installation - assume pre-installed
            logger.info("✅ PostgreSQL packages installed")

            # CloudflareBypasser is embedded - test the embedded class
            try:
                # Test embedded CloudflareBypasser
                test_bypasser = EmbeddedCloudflareBypasser(None, max_retries=1, log=False)
                logger.info("✅ Embedded CloudflareBypasser ready")
            except Exception as e:
                logger.error(f"❌ Embedded CloudflareBypasser test failed: {e}")
                return False

            logger.info("✅ All dependencies ready")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_chromium_options(self):
        """Get Chromium options optimized for VPS"""
        from DrissionPage import ChromiumOptions
        import os

        options = ChromiumOptions().auto_port()

        # Set browser path for snap-installed Chromium
        chromium_paths = [
            '/snap/chromium/current/usr/lib/chromium-browser/chrome',
            '/snap/bin/chromium',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable'
        ]

        for path in chromium_paths:
            if os.path.exists(path):
                options.set_browser_path(path)
                logger.info(f"🌐 Using browser: {path}")
                break

        return options
    
    def setup_browser(self):
        """Setup browser with embedded CloudflareBypasser integration"""
        try:
            # Import required modules
            from DrissionPage import ChromiumPage

            logger.info("📦 Using embedded CloudflareBypasser")

            # Get browser options
            options = self.get_chromium_options()

            # Random User-Agent for better Cloudflare bypass
            import random
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            selected_ua = random.choice(user_agents)
            logger.info(f"🎭 Using User-Agent: {selected_ua[:50]}...")

            # VPS-optimized arguments with headless mode
            arguments = [
                "--headless=new",  # Essential for VPS
                "--no-sandbox",    # Essential for Linux VPS
                "--disable-dev-shm-usage",  # Prevent /dev/shm issues
                "--disable-gpu",
                "--disable-software-rasterizer",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                f"--user-agent={selected_ua}",  # Random User-Agent
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--no-first-run",
                "--force-color-profile=srgb",
                "--metrics-recording-only",
                "--password-store=basic",
                "--use-mock-keychain",
                "--export-tagged-pdf",
                "--no-default-browser-check",
                "--disable-background-mode",
                "--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions",
                "--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",
                "--disable-blink-features=AutomationControlled",  # Anti-detection
                "--disable-web-security",  # Help with Cloudflare
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--deny-permission-prompts",
                "--accept-lang=en-US",
                "--window-size=1920,1080"  # Set window size for headless
            ]

            for argument in arguments:
                options.set_argument(argument)

            # Initialize browser
            self.driver = ChromiumPage(addr_or_opts=options)
            logger.info("🌐 Browser initialized")

            # Set timeouts
            self.driver.set.timeouts(page_load=60, script=30)

            # Initialize embedded CloudflareBypasser with increased retries for VPS
            self.cf_bypasser = EmbeddedCloudflareBypasser(
                driver=self.driver,
                max_retries=5,  # Increased for VPS Cloudflare challenges
                log=True
            )

            logger.info("✅ Embedded CloudflareBypasser setup completed")
            logger.info("🧹 Browser cleanup will be handled automatically on exit")
            return True

        except ImportError as ie:
            logger.error(f"❌ DrissionPage import failed: {ie}")
            logger.error("Make sure DrissionPage is installed: pip install DrissionPage")
            return False
        except Exception as e:
            logger.error(f"❌ Browser setup failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def discover_tools_from_period_pages(self, max_pages_per_period: int = 999, max_tools_limit: int = None) -> List[str]:
        """Discover AI tools from period pages - OLDEST to NEWEST for foundation-first coverage"""
        logger.info("🔍 Starting comprehensive tool discovery from period pages...")
        logger.info("📅 Processing periods from OLDEST to NEWEST for foundation-first coverage")
        logger.info(f"⚙️ Settings: {max_pages_per_period} pages per period, NO TOOL LIMITS (discover ALL tools from ALL pages)")

        all_tools = set()
        processed_periods = 0
        start_time = time.time()

        for period_url in self.period_urls:
            # NO TOOL LIMITS - process ALL periods and extract ALL tools
            logger.info(f"📅 Discovering tools from: {period_url} ({processed_periods+1}/{len(self.period_urls)})")
            logger.info(f"📊 Current total: {len(all_tools)} unique tools discovered so far")
            processed_periods += 1

            try:
                # Navigate to period page
                self.driver.get(period_url)
                time.sleep(5)

                # Handle Cloudflare if needed
                page_title = self.driver.title
                if "just a moment" in page_title.lower():
                    logger.info("🛡️ Cloudflare detected on period page")
                    try:
                        self.cf_bypasser.bypass()
                        time.sleep(5)
                    except Exception as e:
                        logger.warning(f"⚠️ Bypass failed for period page: {e}")
                        continue

                # Extract tools from current page
                page_tools = self.extract_tools_from_page()
                all_tools.update(page_tools)
                logger.info(f"✅ Found {len(page_tools)} tools on page 1")

                # Navigate through pagination - NO TOOL LIMITS
                for page_num in range(2, max_pages_per_period + 1):
                    # NO TOOL LIMITS - extract ALL tools from ALL pages

                    try:
                        # Navigate to next page with correct URL format
                        next_page_url = f"{period_url.rstrip('/')}/page/{page_num}/"
                        logger.info(f"📄 Checking page {page_num}: {next_page_url}")

                        self.driver.get(next_page_url)
                        time.sleep(3)

                        # Check if page exists (404 or redirect)
                        current_url = self.driver.url
                        if f"/page/{page_num}/" not in current_url:
                            logger.info(f"📄 Page {page_num} redirected or not found, stopping pagination")
                            break

                        # Check if page has content and tools
                        html_content = self.driver.html
                        if len(html_content) < 5000:
                            logger.info(f"📄 Page {page_num} appears empty, stopping pagination")
                            break

                        # Check for "no results" or "page not found" indicators
                        if any(indicator in html_content.lower() for indicator in [
                            'no tools found', 'no results', '404', 'page not found',
                            'nothing here', 'no ai tools', 'empty page'
                        ]):
                            logger.info(f"📄 Page {page_num} has no results, stopping pagination")
                            break

                        # Extract tools from this page
                        page_tools = self.extract_tools_from_page()
                        if not page_tools:
                            logger.info(f"📄 No tools found on page {page_num}, stopping pagination")
                            break

                        all_tools.update(page_tools)
                        logger.info(f"✅ Found {len(page_tools)} tools on page {page_num}")

                        # Delay between pages
                        time.sleep(2)

                    except Exception as e:
                        logger.warning(f"⚠️ Error on page {page_num}: {e}")
                        break

                # Progress update
                logger.info(f"📊 Progress: {len(all_tools)} tools discovered from {processed_periods} periods")

                # Delay between periods
                time.sleep(3)

            except Exception as e:
                logger.error(f"❌ Error processing period {period_url}: {e}")
                continue

        discovered_list = list(all_tools)
        total_time = time.time() - start_time

        logger.info(f"🎉 Discovery complete! Found {len(discovered_list)} unique tools total")
        logger.info(f"📊 Processed {processed_periods} periods with deep pagination")
        logger.info(f"📈 Average tools per period: {len(discovered_list)/max(processed_periods,1):.1f}")
        logger.info(f"⏱️ Total time: {total_time/3600:.1f} hours ({total_time/60:.1f} minutes)")
        logger.info(f"🚀 Discovery rate: {len(discovered_list)/max(total_time/60,1):.1f} tools per minute")

        # Display discovered tools summary instead of saving to file
        logger.info(f"📊 Sample discovered tools:")
        for tool in sorted(discovered_list)[:10]:  # Show first 10 tools
            logger.info(f"  ✅ {tool}")
        if len(discovered_list) > 10:
            logger.info(f"  ... and {len(discovered_list) - 10} more tools")

        logger.info("✅ Tools discovery completed (no files created)")
        return discovered_list

    def extract_tools_from_page(self) -> List[str]:
        """Extract tool slugs from current page with popup handling"""
        try:
            # Handle any popup that might appear
            self.handle_login_popup()

            html_content = self.driver.html
            soup = BeautifulSoup(html_content, 'html.parser')

            tools = []

            # Check if we have valid content
            if len(html_content) < 1000:
                logger.warning(f"⚠️ Page content too short ({len(html_content)} chars), may be blocked")
                return []

            # Method 1: Look for links to /ai/ pages
            ai_links = soup.find_all('a', href=re.compile(r'/ai/[^/]+/?$'))
            for link in ai_links:
                href = link.get('href', '')
                slug_match = re.search(r'/ai/([^/]+)/?$', href)
                if slug_match:
                    slug = slug_match.group(1)
                    if slug not in ['', 'index', 'home']:
                        tools.append(slug)

            # Method 2: Look for tool cards or containers
            tool_cards = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|item', re.I))
            for card in tool_cards:
                links = card.find_all('a', href=re.compile(r'/ai/'))
                for link in links:
                    href = link.get('href', '')
                    slug_match = re.search(r'/ai/([^/]+)/?$', href)
                    if slug_match:
                        slug = slug_match.group(1)
                        if slug not in ['', 'index', 'home']:
                            tools.append(slug)

            # Remove duplicates and return
            unique_tools = list(set(tools))
            logger.info(f"🔍 Extracted {len(unique_tools)} unique tools from current page")

            # Log if no tools found (without saving debug files)
            if not unique_tools:
                logger.warning(f"⚠️ No tools found on page (content length: {len(html_content)} chars)")

            return unique_tools

        except Exception as e:
            logger.error(f"❌ Error extracting tools from page: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def bypass_and_extract_with_retries(self, url: str, max_retries: int = 5) -> Optional[Dict]:
        """Extract data with 5 retry attempts - never skip any tool"""
        slug = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]

        for attempt in range(1, max_retries + 1):
            logger.info(f"🔄 Attempt {attempt}/{max_retries} for {slug}")
            start_time = time.time()

            try:
                # Navigate to page
                logger.info(f"🌐 Navigating to: {url}")
                self.driver.get(url)
                time.sleep(8)

                # Check page status
                page_title = self.driver.title
                logger.info(f"📄 Page title: {page_title}")

                # Smart Cloudflare detection
                is_cloudflare, confidence, reason = smart_cloudflare_detection(self.driver, logger)

                if is_cloudflare:
                    logger.info(f"🛡️ Cloudflare detected on attempt {attempt} ({confidence} confidence): {reason}")

                    # Only attempt bypass for high confidence detections
                    if confidence == "low":
                        logger.info("⚠️ Low confidence detection, skipping bypass and continuing...")
                        time.sleep(5)  # Short wait for low confidence
                        # Skip the bypass section
                    else:
                        # Use the existing bypasser with extended timeout
                        try:
                            logger.info("🔧 Starting Cloudflare bypass with extended timeout...")

                            # Use the existing bypasser from setup with timeout
                            if hasattr(self, 'cf_bypasser') and self.cf_bypasser:
                                logger.info("🛡️ Using existing CloudflareBypasser with timeout...")

                                # Try bypass with timeout
                                try:
                                    import threading

                                    bypass_completed = False
                                    bypass_error = None

                                    def run_extract_bypass():
                                        nonlocal bypass_completed, bypass_error
                                        try:
                                            self.cf_bypasser.bypass()
                                            bypass_completed = True
                                        except Exception as e:
                                            bypass_error = e

                                    # Start bypass in thread
                                    bypass_thread = threading.Thread(target=run_extract_bypass)
                                    bypass_thread.daemon = True
                                    bypass_thread.start()

                                    # Wait for bypass with timeout (60 seconds for better success)
                                    bypass_thread.join(timeout=60)

                                    if bypass_thread.is_alive():
                                        logger.warning("⚠️ Extract bypass timeout after 60 seconds")
                                        logger.info("🔄 Continuing with extraction anyway...")
                                    elif bypass_completed:
                                        logger.info("✅ Cloudflare bypass successful!")
                                        # Wait longer for page to fully load after bypass
                                        logger.info("⏳ Waiting for page to fully load after bypass...")
                                        time.sleep(15)  # Increased wait time
                                    elif bypass_error:
                                        logger.warning(f"⚠️ Bypass failed: {bypass_error}")
                                        logger.warning("⚠️ Waiting longer and continuing...")
                                        time.sleep(20)  # Wait even longer if bypass failed
                                    else:
                                        logger.warning("⚠️ Bypass status unknown, continuing...")
                                        time.sleep(15)

                                except Exception as setup_error:
                                    logger.warning(f"⚠️ Bypass setup failed: {setup_error}")
                                    logger.warning("⚠️ Waiting and continuing...")
                                    time.sleep(20)
                            else:
                                logger.warning("⚠️ No bypasser available, using manual navigation...")
                                time.sleep(30)  # Wait longer without bypasser

                            # Check page again after bypass attempt
                            current_title = self.driver.title
                            logger.info(f"📄 Title after bypass: {current_title}")

                            # If still showing Cloudflare, wait even longer
                            if "just a moment" in current_title.lower() or "checking your browser" in current_title.lower():
                                logger.warning("⚠️ Still showing Cloudflare challenge, waiting longer...")
                                time.sleep(25)  # Extended wait for challenge completion

                                # Check one more time
                                final_title = self.driver.title
                                logger.info(f"📄 Final title after extended wait: {final_title}")

                        except Exception as bypass_error:
                            logger.warning(f"⚠️ Bypass error: {bypass_error}")
                            logger.info("⏱️ Waiting longer and continuing...")
                            time.sleep(25)  # Increased wait time on error
                else:
                    logger.info("✅ No Cloudflare protection detected, proceeding with extraction")

                # Wait additional time for page to fully load
                logger.info("⏳ Waiting for page content to fully load...")
                time.sleep(5)

                # Check final page status
                final_title = self.driver.title
                html_content = self.driver.html

                logger.info(f"📄 Final title: {final_title}")
                logger.info(f"📄 HTML length: {len(html_content)} characters")

                # Final smart Cloudflare check
                is_still_cloudflare, final_confidence, final_reason = smart_cloudflare_detection(self.driver, logger)

                if is_still_cloudflare and final_confidence == "high":
                    logger.warning(f"⚠️ Still showing Cloudflare on attempt {attempt}: {final_reason}")
                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying in 20 seconds...")
                        time.sleep(20)
                        continue
                    else:
                        logger.warning(f"⚠️ Still blocked after {max_retries} attempts, but trying extraction anyway...")
                        # Don't return None, try extraction anyway
                elif is_still_cloudflare and final_confidence == "low":
                    logger.info(f"⚠️ Weak Cloudflare indicators detected but continuing: {final_reason}")
                else:
                    logger.info(f"✅ Page ready for extraction: {final_reason}")

                # Validate content length
                if len(html_content) < 3000:  # Reduced threshold
                    logger.warning(f"⚠️ Content too short ({len(html_content)} chars) on attempt {attempt}")
                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying in 10 seconds...")
                        time.sleep(10)
                        continue
                    else:
                        logger.warning(f"⚠️ Content still short after {max_retries} attempts, but trying extraction anyway...")
                        # Don't return None, try extraction anyway

                # Check for basic page elements that indicate successful load
                if "theresanaiforthat.com" in html_content and ("title_inner" in html_content or "h1" in html_content):
                    logger.info("✅ Page appears to be loaded correctly")
                elif attempt < max_retries:
                    logger.warning(f"⚠️ Page may not be fully loaded on attempt {attempt}")
                    logger.info(f"🔄 Retrying in 10 seconds...")
                    time.sleep(10)
                    continue
                else:
                    logger.warning(f"⚠️ Page may not be fully loaded after {max_retries} attempts, but trying extraction anyway...")

                # Log content info (without saving debug files)
                logger.info(f"🔍 Content received: {len(html_content)} chars on attempt {attempt}")

                # Check for valid content (less strict)
                if "theresanaiforthat.com" not in html_content:
                    logger.warning(f"⚠️ Invalid content on attempt {attempt}")
                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying in 10 seconds...")
                        time.sleep(10)
                        continue
                    else:
                        logger.warning(f"⚠️ Content may be invalid but proceeding with extraction...")
                        # Don't return None, try extraction anyway

                # Extract data - ALWAYS TRY EXTRACTION
                logger.info(f"🔍 Extracting data on attempt {attempt}...")
                tool_data = self.extract_exact_data(url)

                if tool_data and tool_data.get('company_name') != "Unknown Tool":
                    duration = time.time() - start_time
                    logger.info(f"✅ SUCCESS on attempt {attempt}! Extracted: {tool_data['company_name']} in {duration:.2f}s")

                    # Debug extracted data
                    logger.info(f"🔍 Quality Score: {tool_data.get('data_quality_score', 0)}/100")
                    logger.info(f"🔍 Company: {tool_data.get('company_name')}")
                    logger.info(f"🔍 Pricing: {tool_data.get('pricing')}")
                    logger.info(f"🔍 Website: {tool_data.get('visit_website_url')}")
                    logger.info(f"🔍 Primary Task: {tool_data.get('primary_task')}")

                    return tool_data
                else:
                    logger.warning(f"⚠️ Data extraction failed on attempt {attempt}")
                    if tool_data:
                        logger.warning(f"⚠️ Got data but company_name is: {tool_data.get('company_name')}")

                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying extraction in 10 seconds...")
                        time.sleep(10)
                        continue
                    else:
                        logger.error(f"❌ All {max_retries} attempts failed - extraction failed")
                        # Return partial data if we have any
                        if tool_data:
                            logger.info("🔄 Returning partial data anyway...")
                            return tool_data
                        return None

            except Exception as e:
                logger.error(f"❌ Error on attempt {attempt}: {e}")
                if attempt < max_retries:
                    logger.info(f"🔄 Retrying due to error in 15 seconds...")
                    time.sleep(15)

                    # Reset browser if needed
                    try:
                        self.driver.refresh()
                        time.sleep(3)
                    except:
                        logger.warning("⚠️ Could not refresh browser")
                    continue
                else:
                    logger.error(f"❌ All {max_retries} attempts failed due to errors")
                    return None

        # This should never be reached, but just in case
        logger.error(f"❌ Unexpected end of retry loop for {slug}")
        return None
    
    def extract_exact_data(self, url: str) -> Optional[Dict]:
        """Extract data matching exact PostgreSQL table structure - FORCED EXTRACTION"""
        try:
            html_content = self.driver.html
            soup = BeautifulSoup(html_content, 'html.parser')

            logger.info("🔍 STARTING FORCED DATA EXTRACTION...")
            logger.info(f"📄 HTML Content Length: {len(html_content)} characters")

            # Initialize with exact table structure
            tool_data = {
                'detail_url': url,
                'crawl_source': 'theresanaiforthat.com',
                'crawl_batch_id': self.batch_id,
                'crawl_date': datetime.now().isoformat(),
                'extraction_success': True
            }

            # Log extraction info (without saving debug files)
            logger.info(f"🔍 Extracting data from content ({len(html_content)} chars)")
            
            # Extract slug from URL
            slug_match = re.search(r'/ai/([^/]+)/?$', url)
            tool_data['slug'] = slug_match.group(1) if slug_match else "unknown"
            logger.info(f"🏷️ Slug: {tool_data['slug']}")

            # Extract ALL required fields for PostgreSQL table

            # 1. COMPANY_NAME (tool name) - REQUIRED
            tool_data['company_name'] = self.extract_tool_name(soup)
            logger.info(f"🏢 Company: {tool_data['company_name']}")

            # CHECK FOR ERROR PAGE - Return None if this is an error page
            if tool_data['company_name'] in ['Page Not Found', 'Not Found', 'Error', '404', 'Unknown Tool']:
                logger.warning(f"❌ DETECTED ERROR PAGE with title: {tool_data['company_name']}")
                logger.warning("❌ This is not a real tool page - returning None")
                return None  # Return None instead of error data

            # 2. DESCRIPTIONS - REQUIRED
            description = self.extract_description(soup)
            tool_data['short_description'] = description[:200] if len(description) > 200 else description
            tool_data['full_description'] = description
            logger.info(f"📝 Description: {description[:100]}...")

            # 3. PRIMARY_TASK (category) - REQUIRED
            tool_data['primary_task'] = self.extract_category(soup)
            logger.info(f"🎯 Primary Task: {tool_data['primary_task']}")

            # 4. APPLICABLE_TASKS as JSONB array - REQUIRED
            tasks = self.extract_applicable_tasks(soup)
            tool_data['applicable_tasks'] = tasks if tasks else []
            logger.info(f"📋 Tasks: {len(tasks)} found")

            # 5. PROS as JSONB array - REQUIRED
            pros = self.extract_pros(soup)
            tool_data['pros'] = pros if pros else []
            logger.info(f"✅ Pros: {len(pros)} found")

            # 6. CONS as JSONB array - REQUIRED
            cons = self.extract_cons(soup)
            tool_data['cons'] = cons if cons else []
            logger.info(f"❌ Cons: {len(cons)} found")

            # 7. PRICING - REQUIRED
            tool_data['pricing'] = self.extract_pricing(soup, html_content)
            logger.info(f"💰 Pricing: {tool_data['pricing']}")

            # 8. IMAGE URLs - REQUIRED
            tool_data['logo_url'] = f"https://media.theresanaiforthat.com/icons/{tool_data['slug']}.svg?width=100"

            # Extract actual featured image from page
            featured_image_url = self.extract_featured_image(soup)
            if featured_image_url:
                tool_data['featured_image_url'] = featured_image_url
                logger.info(f"🖼️ Featured image extracted from page: {featured_image_url}")
            else:
                tool_data['featured_image_url'] = f"https://media.theresanaiforthat.com/{tool_data['slug']}.png?height=768"
                logger.warning(f"⚠️ No featured image found, using fallback: {tool_data['featured_image_url']}")

            logger.info(f"🖼️ Images: Logo & Featured URLs set - Featured: {tool_data['featured_image_url']}")

            # 9. WEBSITE URL - REQUIRED
            tool_data['visit_website_url'] = self.extract_website_url(soup)
            logger.info(f"🌐 Website: {tool_data['visit_website_url']}")

            # 10. FAQs as JSONB - OPTIONAL
            faqs = self.extract_faqs(soup)
            tool_data['faqs'] = faqs if faqs else None  # Keep as Python list for psycopg2.extras.Json
            logger.info(f"❓ FAQs: {len(faqs) if faqs else 0} found")

            # 11. CLICK_COUNT - DEFAULT
            tool_data['click_count'] = 0

            # 12. IS_FEATURED - BOOLEAN
            tool_data['is_featured'] = self.is_featured_tool(html_content.lower())

            # 13. IS_VERIFIED - BOOLEAN
            tool_data['is_verified'] = self.is_verified_tool(html_content.lower())

            # 14. TIMESTAMPS - REQUIRED for Supabase
            tool_data['created_at'] = datetime.now()
            tool_data['updated_at'] = datetime.now()

            # 15. DATA_QUALITY_SCORE - for logging only (not in Supabase table)
            data_quality_score = self.calculate_quality_score(tool_data)

            logger.info(f"📊 ALL FIELDS EXTRACTED - Quality: {data_quality_score}/100")

            logger.info(f"✅ Extraction completed - Quality: {data_quality_score}/100")
            return tool_data
            
        except Exception as e:
            logger.error(f"❌ Data extraction error: {e}")
            return None

    def extract_tool_name(self, soup: BeautifulSoup) -> str:
        """Extract tool name (company_name) - ENHANCED VERSION"""
        logger.info("🔍 Extracting tool name...")

        # Method 1: H1 with class 'title_inner'
        h1_title = soup.find('h1', class_='title_inner')
        if h1_title:
            name = h1_title.get_text(strip=True)
            logger.info(f"📄 Found title_inner: {name}")
            if name and len(name) > 2:
                # Clean the name
                clean_name = re.sub(r'\s*-\s*(AI Tool|There\'s An AI).*$', '', name, flags=re.IGNORECASE)
                logger.info(f"✅ Extracted from title_inner: {clean_name}")
                return clean_name

        # Method 2: Any H1 element
        h1 = soup.find('h1')
        if h1:
            name = h1.get_text(strip=True)
            logger.info(f"📄 Found H1: {name}")
            if name and len(name) > 2:
                # Clean the name
                clean_name = re.sub(r'\s*-\s*(AI Tool|There\'s An AI).*$', '', name, flags=re.IGNORECASE)
                logger.info(f"✅ Extracted from H1: {clean_name}")
                return clean_name

        # Method 3: Meta og:title (but skip if it's a category page)
        og_title = soup.find('meta', {'property': 'og:title'})
        if og_title:
            name = og_title.get('content', '').strip()
            logger.info(f"📄 Found og:title: {name}")
            # Skip if this looks like a category page
            if name and len(name) > 2 and not any(skip in name.lower() for skip in ['there are', 'ai tools for', 'tools for']):
                # Clean the name
                clean_name = re.sub(r'\s*-\s*(AI Tool|There\'s An AI).*$', '', name, flags=re.IGNORECASE)
                logger.info(f"✅ Extracted from og:title: {clean_name}")
                return clean_name
            else:
                logger.warning(f"⚠️ Skipping og:title as it appears to be a category page: {name}")

        # Method 4: Page title (but skip if it's a category page)
        title_tag = soup.find('title')
        if title_tag:
            name = title_tag.get_text(strip=True)
            logger.info(f"📄 Found title tag: {name}")
            # Skip if this looks like a category page
            if name and len(name) > 2 and not any(skip in name.lower() for skip in ['there are', 'ai tools for', 'tools for']):
                # Clean the name
                clean_name = re.sub(r'\s*-\s*(AI Tool|There\'s An AI).*$', '', name, flags=re.IGNORECASE)
                logger.info(f"✅ Extracted from title tag: {clean_name}")
                return clean_name
            else:
                logger.warning(f"⚠️ Skipping title tag as it appears to be a category page: {name}")

        # Method 5: Extract from URL
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(self.driver.url)
            if '/ai/' in parsed_url.path:
                slug = parsed_url.path.split('/ai/')[-1].strip('/')
                if slug:
                    name = slug.replace('-', ' ').title()
                    logger.info(f"✅ Extracted from URL slug: {name}")
                    return name
        except:
            pass

        logger.warning("⚠️ Could not extract tool name from any source")
        return "Unknown Tool"

    def extract_description(self, soup: BeautifulSoup) -> str:
        """Extract description"""
        # Meta description
        meta_desc = soup.find('meta', {'name': 'description'})
        if meta_desc:
            desc = meta_desc.get('content', '').strip()
            if desc and len(desc) > 20:
                return desc

        # OG description
        og_desc = soup.find('meta', {'property': 'og:description'})
        if og_desc:
            desc = og_desc.get('content', '').strip()
            if desc and len(desc) > 20:
                return desc

        return "No description available"

    def extract_category(self, soup: BeautifulSoup) -> str:
        """Extract primary category from related_tasks section or task_component elements"""

        # Method 1: Look for the first task in related_tasks section (most relevant)
        related_tasks_section = soup.find('div', {'id': 'related_tasks'})
        if related_tasks_section:
            logger.info("🔍 Found related_tasks section for primary task extraction")
            # Get the first task_component as the primary task
            first_task_component = related_tasks_section.find('a', class_='task_component')
            if first_task_component:
                related_name = first_task_component.find('span', class_='related_name_inner')
                if related_name:
                    task_text = related_name.get_text(strip=True)
                    # Remove emoji and clean text
                    task_clean = re.sub(r'[^\w\s-]', '', task_text).strip()
                    if task_clean and len(task_clean) > 2 and task_clean not in ['AI Tools', 'Home', '']:
                        logger.info(f"🎯 Found primary task from related_tasks section: {task_clean}")
                        return task_clean

        # Method 2: Look for any task_component with related_name_inner (fallback)
        task_components = soup.find_all('a', class_='task_component')
        if task_components:
            for component in task_components:
                related_name = component.find('span', class_='related_name_inner')
                if related_name:
                    task_text = related_name.get_text(strip=True)
                    # Remove emoji and clean text
                    task_clean = re.sub(r'[^\w\s-]', '', task_text).strip()
                    if task_clean and len(task_clean) > 2 and task_clean not in ['AI Tools', 'Home', '']:
                        logger.info(f"🎯 Found primary task from task_component: {task_clean}")
                        return task_clean

        # Method 3: Look for category links with /s/ pattern (fallback)
        category_links = soup.find_all('a', href=re.compile(r'/s/'))
        if category_links:
            for link in category_links:
                category = link.get_text(strip=True)
                # Clean category text and remove emojis
                category_clean = re.sub(r'[^\w\s-]', '', category).strip()
                if category_clean and len(category_clean) > 2 and category_clean not in ['AI Tools', 'Home', '']:
                    logger.info(f"🎯 Found primary task from category link: {category_clean}")
                    return category_clean

        # Method 4: Look for breadcrumb or navigation elements
        breadcrumbs = soup.find_all(['nav', 'ol', 'ul'], class_=re.compile(r'breadcrumb|nav|category'))
        for breadcrumb in breadcrumbs:
            links = breadcrumb.find_all('a')
            for link in links:
                text = link.get_text(strip=True)
                text_clean = re.sub(r'[^\w\s-]', '', text).strip()
                if text_clean and len(text_clean) > 2 and text_clean not in ['AI Tools', 'Home', 'Tools', '']:
                    logger.info(f"🎯 Found primary task from breadcrumb: {text_clean}")
                    return text_clean

        logger.warning("⚠️ No primary task found, using default")
        return "AI Tools"

    def extract_applicable_tasks(self, soup: BeautifulSoup) -> List[str]:
        """Extract applicable tasks from related_tasks section specifically"""
        tasks = []

        # Method 1: Look for the specific related_tasks section with id="related_tasks"
        related_tasks_section = soup.find('div', {'id': 'related_tasks'})
        if related_tasks_section:
            logger.info("🔍 Found related_tasks section with id='related_tasks'")
            # Get all task_component links within this section
            task_components = related_tasks_section.find_all('a', class_='task_component')
            for component in task_components:
                related_name = component.find('span', class_='related_name_inner')
                if related_name:
                    task_text = related_name.get_text(strip=True)
                    # Remove emoji and clean text
                    task_clean = re.sub(r'[^\w\s-]', '', task_text).strip()
                    if task_clean and len(task_clean) > 2 and task_clean not in ['AI Tools', 'Home', ''] and task_clean not in tasks:
                        tasks.append(task_clean)
                        logger.info(f"📋 Found related task: {task_clean}")

        # Method 2: If no related_tasks section found, look for task_component elements anywhere
        if not tasks:
            logger.info("🔍 No related_tasks section found, searching for task_component elements")
            task_components = soup.find_all('a', class_='task_component')
            for component in task_components:
                related_name = component.find('span', class_='related_name_inner')
                if related_name:
                    task_text = related_name.get_text(strip=True)
                    # Remove emoji and clean text
                    task_clean = re.sub(r'[^\w\s-]', '', task_text).strip()
                    if task_clean and len(task_clean) > 2 and task_clean not in ['AI Tools', 'Home', ''] and task_clean not in tasks:
                        tasks.append(task_clean)

        # Method 3: Fallback to category links with /s/ pattern
        if not tasks:
            logger.info("🔍 No task_component found, falling back to category links")
            category_links = soup.find_all('a', href=re.compile(r'/s/'))
            for link in category_links:
                task = link.get_text(strip=True)
                # Clean task text and remove emojis
                task_clean = re.sub(r'[^\w\s-]', '', task).strip()
                if task_clean and len(task_clean) > 2 and task_clean not in ['AI Tools', 'Home', ''] and task_clean not in tasks:
                    tasks.append(task_clean)

        # Method 4: If still no tasks found, use primary task
        if not tasks:
            logger.info("🔍 No applicable tasks found, using primary task")
            primary = self.extract_category(soup)
            if primary != "AI Tools":
                tasks.append(primary)

        logger.info(f"📋 Final applicable tasks: {tasks}")
        return tasks[:5]  # Limit to 5 tasks

    def extract_pros(self, soup: BeautifulSoup) -> List[str]:
        """Extract pros as array from theresanaiforthat.com structure"""
        pros = []

        # Method 1: Look for specific pros-and-cons section
        pros_cons_section = soup.find('section', {'id': 'pros-and-cons'})
        if pros_cons_section:
            logger.info("🔍 Found pros-and-cons section")
            # Find the pros container
            pros_container = pros_cons_section.find('div', class_='pac-info-item-pros')
            if pros_container:
                # Extract all pac-elem divs within pros container (including hidden ones)
                pros_elements = pros_container.find_all('div', class_='pac-elem')
                for elem in pros_elements:
                    pro_text = elem.get_text(strip=True)
                    if len(pro_text) > 5 and not any(skip in pro_text.lower() for skip in ['view more', 'show more', 'expand']):
                        pros.append(pro_text[:200])
                        if len(pros) >= 15:  # Increased limit to capture more pros
                            break

                # Also check for hidden elements in .more_elements or similar
                more_elements = pros_container.find_all('div', class_=re.compile(r'more|hidden|collapsed'))
                for container in more_elements:
                    hidden_pros = container.find_all('div', class_='pac-elem')
                    for elem in hidden_pros:
                        pro_text = elem.get_text(strip=True)
                        if len(pro_text) > 5 and not any(skip in pro_text.lower() for skip in ['view more', 'show more', 'expand']):
                            pros.append(pro_text[:200])
                            if len(pros) >= 15:
                                break

        # Method 2: Look for H2 sections with pros (fallback)
        if not pros:
            h2_tags = soup.find_all('h2')
            for h2 in h2_tags:
                h2_text = h2.get_text(strip=True).lower()
                if any(word in h2_text for word in ['pros', 'advantages', 'benefits', 'positive']):
                    next_content = h2.find_next_sibling()
                    if next_content:
                        text = next_content.get_text(strip=True)
                        if len(text) > 20:
                            # Split into individual pros
                            pros_list = re.split(r'[•\n\r]+', text)
                            for pro in pros_list:
                                pro = pro.strip()
                                if len(pro) > 10:
                                    pros.append(pro[:200])
                            break

        logger.info(f"✅ Pros: {len(pros)} found")
        return pros[:15]  # Limit to 15 pros

    def extract_cons(self, soup: BeautifulSoup) -> List[str]:
        """Extract cons as array from theresanaiforthat.com structure"""
        cons = []

        # Method 1: Look for specific pros-and-cons section
        pros_cons_section = soup.find('section', {'id': 'pros-and-cons'})
        if pros_cons_section:
            logger.info("🔍 Found pros-and-cons section")
            # Find the cons container
            cons_container = pros_cons_section.find('div', class_='pac-info-item-cons')
            if cons_container:
                # Extract all pac-elem divs within cons container (including hidden ones)
                cons_elements = cons_container.find_all('div', class_='pac-elem')
                for elem in cons_elements:
                    con_text = elem.get_text(strip=True)
                    if len(con_text) > 5 and not any(skip in con_text.lower() for skip in ['view more', 'show more', 'expand']):
                        cons.append(con_text[:200])
                        if len(cons) >= 15:  # Increased limit to capture more cons
                            break

                # Also check for hidden elements in .more_elements or similar
                more_elements = cons_container.find_all('div', class_=re.compile(r'more|hidden|collapsed'))
                for container in more_elements:
                    hidden_cons = container.find_all('div', class_='pac-elem')
                    for elem in hidden_cons:
                        con_text = elem.get_text(strip=True)
                        if len(con_text) > 5 and not any(skip in con_text.lower() for skip in ['view more', 'show more', 'expand']):
                            cons.append(con_text[:200])
                            if len(cons) >= 15:
                                break

        # Method 2: Look for H2 sections with cons (fallback)
        if not cons:
            h2_tags = soup.find_all('h2')
            for h2 in h2_tags:
                h2_text = h2.get_text(strip=True).lower()
                if any(word in h2_text for word in ['cons', 'disadvantages', 'limitations', 'negative']):
                    next_content = h2.find_next_sibling()
                    if next_content:
                        text = next_content.get_text(strip=True)
                        if len(text) > 20:
                            # Split into individual cons
                            cons_list = re.split(r'[•\n\r]+', text)
                            for con in cons_list:
                                con = con.strip()
                                if len(con) > 10:
                                    cons.append(con[:200])
                            break

        logger.info(f"✅ Cons: {len(cons)} found")
        return cons[:15]  # Limit to 15 cons

    def extract_pricing(self, soup: BeautifulSoup, content: str) -> str:
        """Extract detailed pricing information from pricing-options section"""
        pricing_info = []

        # Method 1: Look for specific pricing-options section with id="pricing-options"
        pricing_section = soup.find('section', {'id': 'pricing-options'})
        if pricing_section:
            logger.info("🔍 Found pricing-options section with id='pricing-options'")

            # Extract pricing model
            pricing_rows = pricing_section.find_all('div', class_='pricing-row')
            for row in pricing_rows:
                label_elem = row.find('div', class_='pricing-label')
                value_elem = row.find('div', class_='pricing-value')

                if label_elem and value_elem:
                    label = label_elem.get_text(strip=True)
                    value = value_elem.get_text(strip=True)

                    if label and value:
                        pricing_info.append(f"{label}: {value}")

            # Extract trial duration if available
            trial_row = pricing_section.find('div', class_='trial-duration-row')
            if trial_row:
                label_elem = trial_row.find('div', class_='pricing-label')
                value_elem = trial_row.find('div', class_='pricing-value')

                if label_elem and value_elem:
                    label = label_elem.get_text(strip=True)
                    value = value_elem.get_text(strip=True)

                    if label and value:
                        pricing_info.append(f"{label}: {value}")

            # Combine all pricing information
            if pricing_info:
                combined_pricing = " | ".join(pricing_info)
                logger.info(f"💰 Extracted detailed pricing: {combined_pricing}")
                return combined_pricing

        # Method 2: Look for H2 "Pricing" section (fallback)
        h2_tags = soup.find_all('h2')
        for h2 in h2_tags:
            if 'pricing' in h2.get_text(strip=True).lower():
                next_content = h2.find_next_sibling()
                if next_content:
                    pricing_text = next_content.get_text(strip=True)
                    if len(pricing_text) > 5:
                        logger.info(f"💰 Found pricing from H2 section: {pricing_text[:100]}")
                        return pricing_text[:200]  # Increased limit for more detail

        # Method 3: Look for specific pricing patterns in content
        content_lower = content.lower()
        pricing_patterns = [
            r'free\s*trial.*?\$\d+(?:\.\d+)?(?:/mo|/month)',
            r'free\s*\+\s*from\s*\$\d+(?:\.\d+)?(?:/mo|/month)',
            r'from\s*\$\d+(?:\.\d+)?(?:/mo|/month)',
            r'\$\d+(?:\.\d+)?(?:/mo|/month|/year)',
            r'100%\s*free',
            r'freemium',
            r'free\s*trial'
        ]

        for pattern in pricing_patterns:
            match = re.search(pattern, content_lower)
            if match:
                logger.info(f"💰 Found pricing pattern: {match.group(0)}")
                return match.group(0)

        # Method 4: Default analysis
        if 'free' in content_lower and '$' in content_lower:
            return 'freemium'
        elif 'free' in content_lower:
            return 'free'
        elif '$' in content_lower:
            return 'paid'
        else:
            return 'freemium'

    def clean_url_tracking_params(self, url: str) -> str:
        """Remove tracking parameters from URL"""
        if not url:
            return url

        try:
            from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

            # Parse the URL
            parsed = urlparse(url)

            # Get query parameters
            query_params = parse_qs(parsed.query)

            # Remove common tracking parameters
            tracking_params = [
                'ref', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'msclkid', 'twclid', 'li_fat_id', '_ga', '_gl',
                'mc_cid', 'mc_eid', 'campaign_id', 'ad_id', 'adset_id', 'creative_id',
                'keyword', 'matchtype', 'network', 'device', 'placement', 'target',
                'source', 'medium', 'campaign', 'content', 'term'
            ]

            # Filter out tracking parameters
            clean_params = {k: v for k, v in query_params.items()
                          if k.lower() not in tracking_params}

            # Rebuild query string
            clean_query = urlencode(clean_params, doseq=True) if clean_params else ''

            # Rebuild URL
            clean_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, clean_query, parsed.fragment
            ))

            logger.info(f"🧹 Cleaned URL: {url} -> {clean_url}")
            return clean_url

        except Exception as e:
            logger.warning(f"⚠️ URL cleaning failed: {e}, returning original URL")
            return url

    def extract_website_url(self, soup: BeautifulSoup) -> str:
        """Extract and clean website URL"""
        # Button with classes 'ai_top_link' and 'visit_website_btn'
        visit_btn = soup.find('a', class_=['ai_top_link', 'visit_website_btn'])
        if visit_btn and visit_btn.get('href'):
            href = visit_btn.get('href')
            if href.startswith('http') and 'theresanaiforthat.com' not in href:
                clean_url = self.clean_url_tracking_params(href)
                logger.info(f"🔗 Found website URL from visit button: {clean_url}")
                return clean_url

        # Any button with text 'Use tool'
        use_tool_btns = soup.find_all('a', string=re.compile(r'use tool', re.I))
        for btn in use_tool_btns:
            href = btn.get('href', '')
            if href.startswith('http') and 'theresanaiforthat.com' not in href:
                clean_url = self.clean_url_tracking_params(href)
                logger.info(f"🔗 Found website URL from use tool button: {clean_url}")
                return clean_url

        # Look for buttons with specific text patterns
        button_patterns = [
            r'visit\s+website', r'go\s+to\s+website', r'official\s+website',
            r'try\s+now', r'get\s+started', r'launch\s+app', r'open\s+app'
        ]

        for pattern in button_patterns:
            buttons = soup.find_all('a', string=re.compile(pattern, re.I))
            for btn in buttons:
                href = btn.get('href', '')
                if href.startswith('http') and 'theresanaiforthat.com' not in href:
                    clean_url = self.clean_url_tracking_params(href)
                    logger.info(f"🔗 Found website URL from button pattern '{pattern}': {clean_url}")
                    return clean_url

        # First external link (fallback)
        external_links = soup.find_all('a', href=lambda x: x and x.startswith('http'))
        for link in external_links:
            href = link.get('href', '')
            skip_domains = [
                'theresanaiforthat.com', 'cloudflare.com', 'facebook.com',
                'twitter.com', 'linkedin.com', 'youtube.com', 'instagram.com',
                'github.com', 'discord.com', 'reddit.com', 'medium.com'
            ]
            if not any(domain in href.lower() for domain in skip_domains):
                clean_url = self.clean_url_tracking_params(href)
                logger.info(f"🔗 Found website URL from external link: {clean_url}")
                return clean_url

        logger.warning("⚠️ No website URL found")
        return None

    def extract_featured_image(self, soup: BeautifulSoup) -> str:
        """Extract featured image URL from page"""
        try:
            logger.info("🔍 Starting featured image extraction...")
            # Priority order for featured image extraction - specific to theresanaiforthat.com
            image_selectors = [
                # THE ACTUAL IMAGE LOCATION on theresanaiforthat.com (highest priority)
                'img.ai_image',  # This is where the main tool image is!
                '#image_ai_link img',  # Alternative selector for the same image

                # Open Graph image (backup)
                'meta[property="og:image"]',
                'meta[name="twitter:image"]',

                # Other possible locations (fallbacks)
                'section[data-sentry-component="ProductGallery"] img',
                'div[data-sentry-component="Gallery"] img',
                'section.overflow-hidden img',
                'div.relative.overflow-hidden img',

                # Gallery and screenshot sections
                '.gallery img:first-child',
                '.screenshots img:first-child',
                'img[alt*="screenshot"]',
                'img[alt*="gallery"]',

                # Hero/featured images
                '.hero-image img',
                '.featured-image img',
                '.product-screenshot img',
                '.main-image img',

                # Images with loading priority (usually featured)
                'img[loading="eager"]',
                'img[fetchpriority="high"]',

                # Rounded images (common pattern)
                'img[class*="rounded"]',
                'img[class*="rounded-xl"]',

                # General content images (larger ones)
                'img[width][height]',
                'img[style*="width"]',

                # Fallback to any content image
                'main img',
                'article img',
                '.content img'
            ]

            for selector in image_selectors:
                elem = soup.select_one(selector)
                if elem:
                    img_url = ""

                    if elem.name == 'meta':
                        img_url = elem.get('content', '')
                    else:
                        # Handle srcset first (higher quality)
                        srcset = elem.get('srcset', '')
                        if srcset:
                            # Extract highest quality URL from srcset
                            srcset_urls = srcset.split(',')
                            if srcset_urls:
                                # Try to get the highest resolution
                                best_url = ""
                                max_width = 0

                                for url_entry in srcset_urls:
                                    url_entry = url_entry.strip()
                                    parts = url_entry.split(' ')
                                    if len(parts) >= 2:
                                        url = parts[0]
                                        width_str = parts[1].replace('w', '')
                                        try:
                                            width = int(width_str)
                                            if width > max_width:
                                                max_width = width
                                                best_url = url
                                        except:
                                            pass

                                img_url = best_url or srcset_urls[0].strip().split(' ')[0]
                        else:
                            img_url = elem.get('src', '') or elem.get('data-src', '')

                    if img_url:
                        # Clean and validate URL
                        if img_url.startswith('//'):
                            img_url = 'https:' + img_url
                        elif img_url.startswith('/'):
                            img_url = 'https://theresanaiforthat.com' + img_url

                        # Skip very small images (likely icons) but NOT the main ai_image
                        if any(size in img_url.lower() for size in ['16x16', '32x32', '64x64', 'favicon']) and 'ai_image' not in (elem.get('class', []) if hasattr(elem, 'get') else []):
                            continue

                        logger.info(f"✅ Found featured image via {selector}: {img_url}")
                        return img_url

            logger.warning("⚠️ No featured image found on page using any selector")
            logger.info(f"🔍 Tried {len(image_selectors)} different selectors for image extraction")
            return ""

        except Exception as e:
            logger.error(f"❌ Error extracting featured image: {e}")
            return ""

    def extract_faqs(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract FAQs as structured data"""
        faqs = []

        # Method 1: Look for specific FAQ section with id="faq"
        faq_section = soup.find('section', {'id': 'faq'})
        if faq_section:
            logger.info("🔍 Found FAQ section with id='faq'")
            faq_items = faq_section.find_all('div', class_='faq-info')

            for item in faq_items:
                # Skip "Show more" and "Ask question" items
                if any(cls in item.get('class', []) for cls in ['faq_show_more', 'ask_question']):
                    continue

                title_elem = item.find('div', class_='faq-info-title')
                desc_elem = item.find('div', class_='faq-info-description')

                if title_elem and desc_elem:
                    question = title_elem.get_text(strip=True)
                    answer = desc_elem.get_text(strip=True)

                    if len(question) > 5 and len(answer) > 10:
                        faqs.append({
                            'question': question[:200],
                            'answer': answer[:500]
                        })

                        if len(faqs) >= 10:  # Limit to 10 FAQs
                            break

        # Method 2: Look for Q&A sections
        if not faqs:
            qa_headers = soup.find_all(['h2', 'h3'], string=re.compile(r'q\s*&\s*a|faq|frequently asked', re.I))
            for header in qa_headers:
                parent = header.find_parent(['section', 'div'])
                if parent:
                    # Look for question-answer pairs
                    questions = parent.find_all(['div', 'h3', 'h4'], string=re.compile(r'\?', re.I))
                    for question in questions:
                        q_text = question.get_text(strip=True)
                        if len(q_text) > 10:
                            # Find answer (next sibling)
                            answer_elem = question.find_next_sibling()
                            if answer_elem:
                                a_text = answer_elem.get_text(strip=True)
                                if len(a_text) > 10:
                                    faqs.append({
                                        'question': q_text[:200],
                                        'answer': a_text[:500]
                                    })

                                    if len(faqs) >= 10:
                                        break

        # Method 3: General FAQ search
        if not faqs:
            faq_sections = soup.find_all(['div', 'section'], string=re.compile(r'faq|frequently asked|questions?', re.I))
            for section in faq_sections:
                parent = section.parent if section.parent else section
                questions = parent.find_all(['h3', 'h4', 'strong'], string=re.compile(r'\?', re.I))
                for question in questions:
                    q_text = question.get_text(strip=True)
                    if len(q_text) > 10:
                        answer_elem = question.find_next_sibling()
                        if not answer_elem:
                            answer_elem = question.parent.find_next_sibling()

                        if answer_elem:
                            a_text = answer_elem.get_text(strip=True)
                            if len(a_text) > 10:
                                faqs.append({
                                    'question': q_text[:200],
                                    'answer': a_text[:500]
                                })

                                if len(faqs) >= 5:
                                    break

        return faqs

    def is_featured_tool(self, _content_lower: str) -> bool:
        """Check if tool is featured - Always returns False as requested"""
        return False

    # ==========================================
    # INTELLIGENT SEARCH SYSTEM
    # ==========================================

    def search_tool_intelligent(self, tool_name: str, tool_url: str = None) -> Optional[Dict]:
        """
        🔍 Intelligent multi-source search for AI tools

        Search Priority:
        1. Database (existing tools)
        2. TheresAnAIForThat.com
        3. ProductHunt
        4. General web search
        5. LLM enhancement for missing data
        """
        if not self.enable_intelligent_search:
            logger.warning("⚠️ Intelligent search is disabled in configuration")
            return None

        logger.info(f"🔍 Starting intelligent search for: {tool_name}")

        # Step 1: Check if tool exists in database (for update tracking)
        logger.info("🗄️ Checking if tool exists in database...")
        db_result = self.search_database(tool_name, tool_url)
        tool_exists_in_db = bool(db_result)

        if tool_exists_in_db:
            logger.info("✅ Tool found in database - will update existing record")
        else:
            logger.info("ℹ️ Tool not found in database - will create new record")

        # Continue with extraction regardless of database status

        # Step 2: Search in TheresAnAIForThat.com
        logger.info("🔍 Searching in TheresAnAIForThat.com...")
        taifort_result = self.search_theresanaiforthat(tool_name)
        if taifort_result:
            logger.info("✅ Tool found on TheresAnAIForThat.com")
            # Enhance data with LLM safely
            enhanced_result = self.safe_enhance_tool_data(taifort_result)
            # Save to database (will update if exists)
            self.save_enhanced_tool_data(enhanced_result)
            return enhanced_result

        # Step 3: Search in ProductHunt
        logger.info("🔍 Searching in ProductHunt...")
        ph_result = self.search_producthunt(tool_name)
        if ph_result:
            logger.info("✅ Tool found on ProductHunt")
            # The comprehensive new system is applied inside search_producthunt
            # Save to database (will update if exists)
            self.save_enhanced_tool_data(ph_result)
            return ph_result

        # Step 4: General web search
        logger.info("🔍 Searching in web search engines...")
        web_result = self.search_web(tool_name, tool_url)
        if web_result:
            logger.info("✅ Tool found via web search")
            # Enhance data with LLM safely
            enhanced_result = self.safe_enhance_tool_data(web_result)
            # Save to database (will update if exists)
            self.save_enhanced_tool_data(enhanced_result)
            return enhanced_result

        logger.warning(f"❌ Tool not found in any source: {tool_name}")
        return None

    def safe_enhance_tool_data(self, tool_data: Dict) -> Dict:
        """Safely enhance tool data with LLM without stopping the process"""
        try:
            if not self.enable_llm_enhancement:
                logger.info("ℹ️ LLM enhancement disabled, returning original data")
                return tool_data

            logger.info(f"🤖 Enhancing data for: {tool_data.get('company_name', 'Unknown')}")

            # Try to enhance with timeout
            import signal
            import threading

            enhanced_data = tool_data.copy()
            enhancement_completed = False
            enhancement_error = None

            def enhance_with_timeout():
                nonlocal enhanced_data, enhancement_completed, enhancement_error
                try:
                    # استخدام الدالة الموجودة للتحسين
                    if hasattr(self, 'enhance_tool_data_with_llm'):
                        enhanced_data = self.enhance_tool_data_with_llm(tool_data)
                    enhancement_completed = True
                except Exception as e:
                    enhancement_error = e

            # تشغيل التحسين في thread منفصل مع timeout
            enhancement_thread = threading.Thread(target=enhance_with_timeout)
            enhancement_thread.daemon = True
            enhancement_thread.start()

            # انتظار لمدة 30 ثانية كحد أقصى
            enhancement_thread.join(timeout=30)

            if enhancement_thread.is_alive():
                logger.warning("⚠️ LLM enhancement timeout after 30 seconds, continuing with original data")
                return tool_data
            elif enhancement_completed:
                logger.info("✅ LLM enhancement completed successfully")
                return enhanced_data
            elif enhancement_error:
                logger.warning(f"⚠️ LLM enhancement failed: {enhancement_error}, continuing with original data")
                return tool_data
            else:
                logger.warning("⚠️ LLM enhancement status unknown, continuing with original data")
                return tool_data

        except Exception as e:
            logger.warning(f"⚠️ Safe enhancement error: {e}, continuing with original data")
            return tool_data

    def search_database(self, query: str, tool_url: str = None) -> Optional[Dict]:
        """Search for tool in existing Supabase database"""
        try:
            # Try Supabase PostgreSQL first
            if POSTGRES_AVAILABLE and all(self.supabase_config.values()):
                logger.info("🔍 Connecting to Supabase PostgreSQL...")
                try:
                    conn = psycopg2.connect(**self.supabase_config)
                    cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

                    # Search by name or URL
                    if tool_url:
                        cursor.execute("""
                            SELECT * FROM tools
                            WHERE LOWER(company_name) ILIKE %s
                            OR visit_website_url ILIKE %s
                            LIMIT 1
                        """, (f"%{query.lower()}%", f"%{tool_url}%"))
                    else:
                        cursor.execute("""
                            SELECT * FROM tools
                            WHERE LOWER(company_name) ILIKE %s
                            OR LOWER(short_description) ILIKE %s
                            LIMIT 1
                        """, (f"%{query.lower()}%", f"%{query.lower()}%"))

                    result = cursor.fetchone()
                    conn.close()

                    if result:
                        logger.info(f"✅ Found tool in Supabase: {result.get('company_name')}")
                        tool_dict = dict(result)
                        # Ensure slug exists
                        if not tool_dict.get('slug'):
                            tool_dict['slug'] = tool_dict.get('company_name', 'unknown').lower().replace(' ', '-').replace('_', '-')
                        return tool_dict
                    else:
                        logger.info("ℹ️ Tool not found in Supabase database")

                except Exception as e:
                    logger.warning(f"⚠️ Supabase connection failed: {e}")
            else:
                logger.info("ℹ️ Supabase not configured, trying local database...")

            # Fallback to local SQLite database
            try:
                conn = sqlite3.connect('ai_tools_exact.db', timeout=10.0)
                cursor = conn.cursor()

                # Search by name or URL
                if tool_url:
                    cursor.execute("""
                        SELECT * FROM tools
                        WHERE LOWER(company_name) LIKE ?
                        OR visit_website_url LIKE ?
                        LIMIT 1
                    """, (f"%{query.lower()}%", f"%{tool_url}%"))
                else:
                    cursor.execute("""
                        SELECT * FROM tools
                        WHERE LOWER(company_name) LIKE ?
                        OR LOWER(short_description) LIKE ?
                        LIMIT 1
                    """, (f"%{query.lower()}%", f"%{query.lower()}%"))

                result = cursor.fetchone()

                if result:
                    # Convert to dict
                    columns = [description[0] for description in cursor.description]
                    tool_dict = dict(zip(columns, result))
                    # Ensure slug exists
                    if not tool_dict.get('slug'):
                        tool_dict['slug'] = tool_dict.get('company_name', 'unknown').lower().replace(' ', '-').replace('_', '-')
                    logger.info(f"✅ Found tool in local database: {tool_dict.get('company_name')}")
                    conn.close()
                    return tool_dict
                else:
                    logger.info("ℹ️ Tool not found in local database")

                conn.close()

            except Exception as e:
                logger.warning(f"⚠️ Local database search failed: {e}")

        except Exception as e:
            logger.error(f"❌ Database search error: {e}")

        return None

    def search_theresanaiforthat(self, query: str) -> Optional[Dict]:
        """Search tool on TheresAnAIForThat.com using existing crawler with proper Cloudflare bypass"""
        try:
            # Try direct URL first if it looks like a tool name
            direct_url = f"https://theresanaiforthat.com/ai/{query.lower().replace(' ', '-')}/"

            logger.info(f"🔍 Trying direct URL: {direct_url}")

            # Try to use existing browser if available
            if hasattr(self, 'driver') and self.driver:
                try:
                    # Try direct URL first with proper Cloudflare bypass
                    logger.info("🛡️ Bypassing Cloudflare protection...")
                    self.driver.get(direct_url)
                    time.sleep(3)  # Initial wait

                    # Try bypass if available with timeout
                    if hasattr(self, 'cf_bypasser') and self.cf_bypasser:
                        try:
                            logger.info("🔧 Starting Cloudflare bypass with timeout...")

                            # Use threading to implement timeout
                            import threading
                            import signal

                            bypass_completed = False
                            bypass_error = None

                            def run_bypass():
                                nonlocal bypass_completed, bypass_error
                                try:
                                    self.cf_bypasser.bypass()
                                    bypass_completed = True
                                    logger.info("✅ Cloudflare bypass completed")
                                except Exception as e:
                                    bypass_error = e

                            # Start bypass in thread
                            bypass_thread = threading.Thread(target=run_bypass)
                            bypass_thread.daemon = True
                            bypass_thread.start()

                            # Wait for bypass with timeout (90 seconds for VPS)
                            bypass_thread.join(timeout=90)

                            if bypass_thread.is_alive():
                                logger.warning("⚠️ Cloudflare bypass timeout after 90 seconds")
                                logger.info("🔄 Continuing with page as-is...")
                                success = False
                            elif bypass_completed:
                                logger.info("✅ Cloudflare bypass successful!")
                                success = True
                            elif bypass_error:
                                logger.warning(f"⚠️ Bypass failed: {bypass_error}")
                                success = False
                            else:
                                logger.warning("⚠️ Bypass status unknown, continuing...")
                                success = False

                        except Exception as e:
                            logger.warning(f"⚠️ Bypass setup failed: {e}")
                            success = False
                    else:
                        logger.warning("⚠️ No bypasser available")
                        success = False

                    # Always wait after bypass attempt (successful or not)
                    logger.info("⏳ Waiting for page to stabilize after bypass...")
                    time.sleep(10)  # Initial wait

                    # Check page status multiple times
                    for check_attempt in range(3):
                        logger.info(f"🔍 Checking page status (attempt {check_attempt + 1}/3)...")

                        try:
                            current_title = self.driver.title
                            html_content = self.driver.html

                            logger.info(f"📄 Current title: {current_title}")
                            logger.info(f"📄 HTML length: {len(html_content)} characters")

                            # Check for Cloudflare indicators (more specific)
                            cloudflare_indicators = [
                                "checking your browser",
                                "just a moment",
                                "please wait while we",
                                "ray id:",
                                "cloudflare-browser-check"
                            ]

                            # Only check for actual blocking indicators, not just "cloudflare" mentions
                            has_cloudflare = any(indicator in html_content.lower() for indicator in cloudflare_indicators)

                            # Additional check: if page has substantial content, it's likely loaded
                            has_substantial_content = len(html_content) > 500000  # 500KB+ suggests real content

                            # If we have substantial content and a proper title, consider it loaded
                            if has_substantial_content and any(word in current_title.lower() for word in ['kling', 'ai tool', 'video']):
                                logger.info("✅ Page appears fully loaded based on content size and title")
                                has_cloudflare = False

                            if has_cloudflare:
                                logger.warning(f"⚠️ Still showing Cloudflare indicators on check {check_attempt + 1}")
                                if check_attempt < 2:  # Not the last attempt
                                    logger.info("⏳ Waiting longer for Cloudflare to complete...")
                                    time.sleep(15)  # Wait longer between checks
                                    continue
                                else:
                                    logger.warning("⚠️ Cloudflare still present after all checks, but continuing...")
                            else:
                                logger.info("✅ No Cloudflare indicators detected")
                                break

                        except Exception as e:
                            logger.warning(f"⚠️ Error checking page status: {e}")
                            if check_attempt < 2:
                                time.sleep(10)
                                continue

                    # Final page check
                    try:
                        final_title = self.driver.title
                        html_content = self.driver.html

                        logger.info(f"📄 Final title: {final_title}")
                        logger.info(f"📄 Final HTML length: {len(html_content)} characters")

                        # SMART PAGE VALIDATION - Check if this is actually a tool page
                        logger.info("🔍 SMART PAGE VALIDATION - Checking if this is a real tool page")

                        # Check basic page validity
                        page_title = final_title.lower()
                        content_size = len(html_content)

                        logger.info(f"📊 Page analysis:")
                        logger.info(f"   - Title: {final_title}")
                        logger.info(f"   - Content size: {content_size} characters")
                        logger.info(f"   - Contains query: {query.lower() in page_title}")
                        logger.info(f"   - Contains 'ai tool': {'ai tool' in page_title}")

                        # Check for 404/error page indicators
                        error_indicators = [
                            'page not found', 'not found', '404', 'error',
                            'does not exist', 'page does not exist',
                            'sorry, the page', 'page you\'re looking for'
                        ]

                        is_error_page = any(indicator in page_title for indicator in error_indicators)

                        # Check if this is a category page (not individual tool page)
                        category_indicators = [
                            'there are', 'ai tools for', 'tools for',
                            f'{content_size//1000}+ ai tools', 'browse ai tools'
                        ]
                        is_category_page = any(indicator in page_title for indicator in category_indicators)

                        # Check if content suggests this is the actual tool
                        is_likely_tool_page = (
                            query.lower() in page_title and not is_category_page or
                            ('ai tool' in page_title and query.lower() in html_content.lower() and not is_category_page) or
                            (content_size > 200000 and not is_error_page and not is_category_page)  # Very large content, likely real page
                        )

                        logger.info(f"🔍 Validation results:")
                        logger.info(f"   - Is error page: {is_error_page}")
                        logger.info(f"   - Is category page: {is_category_page}")
                        logger.info(f"   - Is likely tool page: {is_likely_tool_page}")

                        # Only extract if this appears to be a real tool page
                        if content_size > 50000 and is_likely_tool_page and not is_error_page and not is_category_page:
                            logger.info("✅ REAL TOOL PAGE DETECTED - Proceeding with extraction")

                            soup = BeautifulSoup(html_content, 'html.parser')

                            # Look for title element
                            title_elem = soup.find('h1', class_='title_inner')
                            h1_elem = soup.find('h1')

                            if title_elem:
                                logger.info(f"✅ Found title_inner: {title_elem.get_text(strip=True)}")
                            elif h1_elem:
                                logger.info(f"✅ Found H1: {h1_elem.get_text(strip=True)}")
                            else:
                                logger.info("ℹ️ No H1 found, but proceeding with extraction anyway")

                            # Use existing bypass_and_extract_with_retries function (REUSE EXISTING CODE)
                            logger.info("🔍 Using existing bypass_and_extract_with_retries function...")
                            tool_data = self.bypass_and_extract_with_retries(direct_url, max_retries=2)

                            if tool_data and tool_data.get('company_name') != "Unknown Tool":
                                tool_data['crawl_source'] = 'theresanaiforthat_direct'
                                logger.info(f"✅ EXTRACTION SUCCESS using existing function: {tool_data.get('company_name', 'Unknown')}")
                                logger.info(f"📊 Quality Score: {tool_data.get('data_quality_score', 0)}")
                                return tool_data
                            else:
                                logger.warning("⚠️ Existing extraction function failed - trying basic fallback...")

                                # Use existing basic data creation as fallback
                                basic_data = self.create_basic_tool_data_from_page(soup, direct_url, final_title)
                                if basic_data:
                                    basic_data['crawl_source'] = 'theresanaiforthat_direct_basic'
                                    logger.info(f"✅ BASIC EXTRACTION SUCCESS: {basic_data.get('company_name', 'Unknown')}")
                                    return basic_data
                                else:
                                    logger.error("❌ Both existing function and basic extraction failed")
                        else:
                            if is_error_page:
                                logger.info("❌ ERROR PAGE DETECTED - This is not the tool we're looking for")
                                logger.info("🔄 Will try search instead...")
                            elif is_category_page:
                                logger.info("❌ CATEGORY PAGE DETECTED - This is not an individual tool page")
                                logger.info("🔄 Will try search instead...")
                            else:
                                logger.warning(f"⚠️ Content too small ({content_size} chars) - may be error page")

                    except Exception as e:
                        logger.warning(f"⚠️ Error during direct URL processing: {e}")
                        logger.info("🔄 Continuing to search despite error...")

                    # If direct URL was an error page or failed, try search
                    logger.info("🔍 Direct URL was not successful, trying search...")
                    search_url = f"https://theresanaiforthat.com/search?q={query.replace(' ', '+')}"
                    logger.info(f"🔍 Trying search URL: {search_url}")

                    # Bypass Cloudflare for search page
                    logger.info("🛡️ Bypassing Cloudflare for search...")
                    self.driver.get(search_url)
                    time.sleep(3)  # Initial wait

                    # Try bypass if available with timeout
                    if hasattr(self, 'cf_bypasser') and self.cf_bypasser:
                        try:
                            logger.info("🔧 Starting Cloudflare bypass for search with timeout...")

                            # Use threading to implement timeout
                            import threading

                            bypass_completed = False
                            bypass_error = None

                            def run_search_bypass():
                                nonlocal bypass_completed, bypass_error
                                try:
                                    self.cf_bypasser.bypass()
                                    bypass_completed = True
                                    logger.info("✅ Search Cloudflare bypass completed")
                                except Exception as e:
                                    bypass_error = e

                            # Start bypass in thread
                            bypass_thread = threading.Thread(target=run_search_bypass)
                            bypass_thread.daemon = True
                            bypass_thread.start()

                            # Wait for bypass with timeout (30 seconds for search)
                            bypass_thread.join(timeout=30)

                            if bypass_thread.is_alive():
                                logger.warning("⚠️ Search bypass timeout after 30 seconds")
                                logger.info("🔄 Continuing with search page as-is...")
                                success = False
                            elif bypass_completed:
                                logger.info("✅ Search Cloudflare bypass successful!")
                                success = True
                            elif bypass_error:
                                logger.warning(f"⚠️ Search bypass failed: {bypass_error}")
                                success = False
                            else:
                                logger.warning("⚠️ Search bypass status unknown, continuing...")
                                success = False

                        except Exception as e:
                            logger.warning(f"⚠️ Search bypass setup failed: {e}")
                            success = False
                    else:
                        success = False

                    if success:
                        logger.info("✅ Cloudflare bypass successful for search")

                        # Wait for search results to load
                        logger.info("⏳ Waiting for search results...")
                        time.sleep(10)  # Increased wait time for search

                        html_content = self.driver.html

                        # Check for Cloudflare challenge again
                        if "checking your browser" in html_content.lower() or "cloudflare" in html_content.lower():
                            logger.warning("⚠️ Still showing Cloudflare challenge on search, waiting longer...")
                            time.sleep(20)  # Wait even longer for search
                            html_content = self.driver.html

                        soup = BeautifulSoup(html_content, 'html.parser')

                        # Find search result links - look for various patterns
                        result_selectors = [
                            'a[href*="/ai/"]',  # Direct tool links
                            '.search-result a',  # Search result links
                            '.tool-item a',     # Tool item links
                            'a[href*="theresanaiforthat.com/ai/"]'  # Full tool links
                        ]

                        found_links = []
                        for selector in result_selectors:
                            links = soup.select(selector)
                            for link in links:
                                href = link.get('href', '')
                                if '/ai/' in href and query.lower() in href.lower():
                                    # Ensure full URL
                                    if href.startswith('/'):
                                        tool_url = f"https://theresanaiforthat.com{href}"
                                    elif href.startswith('http'):
                                        tool_url = href
                                    else:
                                        continue

                                    found_links.append(tool_url)

                        if found_links:
                            # Try the first matching link
                            tool_url = found_links[0]
                            logger.info(f"✅ Found tool via search: {tool_url}")

                            # Extract data from this tool page with retries
                            tool_data = self.bypass_and_extract_with_retries(tool_url, max_retries=3)
                            if tool_data and tool_data.get('company_name') != 'Page Not Found':
                                tool_data['crawl_source'] = 'theresanaiforthat_search'
                                return tool_data
                            else:
                                logger.warning("⚠️ Search result was also an error page")
                        else:
                            logger.info("ℹ️ No matching tool links found in search results")
                            # Log some of the page content for debugging
                            logger.info(f"📄 Page content preview: {html_content[:500]}...")
                    else:
                        logger.warning("⚠️ Cloudflare bypass failed for search")

                except Exception as e:
                    logger.warning(f"⚠️ Browser search failed: {e}")
            else:
                logger.warning("⚠️ No browser available for TheresAnAIForThat search")

            # If no browser or search failed, return None to continue to next method
            return None

        except Exception as e:
            logger.error(f"❌ TheresAnAIForThat search error: {e}")
            return None

    def search_producthunt(self, query: str) -> Optional[Dict]:
        """Search tool on ProductHunt with web scraping fallback"""
        logger.info(f"🔍 Searching ProductHunt for: {query}")

        # Try API first if available
        if self.producthunt_key:
            logger.info("🔑 Using ProductHunt API...")
            api_result = self.search_producthunt_api(query)
            if api_result:
                return api_result
        else:
            logger.info("ℹ️ ProductHunt API key not configured, using web search...")

        # Fallback to web scraping ProductHunt
        return self.search_producthunt_web(query)

    def search_producthunt_api(self, query: str) -> Optional[Dict]:
        """Search ProductHunt using API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.producthunt_key}',
                'Content-Type': 'application/json'
            }

            # ProductHunt GraphQL API
            graphql_query = {
                'query': '''
                query($query: String!) {
                    posts(first: 5, filter: {search: $query}) {
                        edges {
                            node {
                                name
                                tagline
                                description
                                url
                                website
                                thumbnail {
                                    url
                                }
                                topics {
                                    edges {
                                        node {
                                            name
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                ''',
                'variables': {'query': query}
            }

            response = requests.post(
                'https://api.producthunt.com/v2/api/graphql',
                headers=headers,
                json=graphql_query,
                timeout=self.search_timeout
            )

            if response.status_code == 200:
                data = response.json()
                posts = data.get('data', {}).get('posts', {}).get('edges', [])

                if posts:
                    post = posts[0]['node']  # Get first result
                    logger.info(f"✅ Found via ProductHunt API: {post.get('name', 'Unknown')}")
                    return self.convert_producthunt_to_tool_data(post)

        except Exception as e:
            logger.error(f"❌ ProductHunt API error: {e}")

        return None

    def search_producthunt_web(self, query: str) -> Optional[Dict]:
        """Search ProductHunt using web scraping"""
        try:
            logger.info("🌐 Searching ProductHunt website...")

            # Setup browser if not already done
            if not hasattr(self, 'driver') or not self.driver:
                if not self.setup_browser():
                    logger.error("❌ Failed to setup browser for ProductHunt search")
                    return None

            # Search URL
            search_url = f"https://www.producthunt.com/search?q={query.replace(' ', '+')}"
            logger.info(f"🔍 ProductHunt search URL: {search_url}")

            # Navigate to search page
            self.driver.get(search_url)

            # انتظار أطول لتحميل المحتوى الديناميكي
            logger.info("⏳ انتظار تحميل المحتوى الديناميكي...")
            time.sleep(10)  # انتظار أطول للمحتوى الديناميكي

            # محاولة التمرير لتحميل المزيد من المحتوى
            try:
                self.driver.scroll.to_bottom()
                time.sleep(3)
                logger.info("📜 تم التمرير لأسفل لتحميل المزيد من المحتوى")
            except:
                pass

            # Get page content
            html_content = self.driver.html
            soup = BeautifulSoup(html_content, 'html.parser')

            # فحص إضافي للمحتوى الديناميكي
            if len(html_content) < 50000:  # إذا كان المحتوى قصير جداً
                logger.warning("⚠️ المحتوى قصير، قد يكون لم يتحمل بالكامل")
                logger.info("🔄 محاولة إعادة تحميل الصفحة...")
                self.driver.refresh()
                time.sleep(15)  # انتظار أطول بعد إعادة التحميل
                html_content = self.driver.html
                soup = BeautifulSoup(html_content, 'html.parser')
                logger.info(f"📄 طول المحتوى بعد إعادة التحميل: {len(html_content)} characters")

            # Debug: Save page content for analysis
            logger.info(f"📄 Page title: {soup.title.get_text() if soup.title else 'No title'}")
            logger.info(f"📄 Page content length: {len(html_content)} characters")

            # Analyze ProductHunt search results page based on actual structure
            logger.info("🔍 Analyzing ProductHunt search results page...")

            # Search for results based on actual structure
            # Each result contains: name, description, rating, link
            search_result_selectors = [
                # Search for containers that contain results
                'div',  # General search in all divs
                'section',
                'article'
            ]

            products = []

            # Smart search for results
            logger.info("🔍 Searching for ProductHunt results...")

            # Strategy 1: Search for elements containing the requested tool name
            query_lower = query.lower()
            all_divs = soup.select('div')

            logger.info(f"📊 Examining {len(all_divs)} divs searching for '{query}'...")

            for div in all_divs:
                div_text = div.get_text(strip=True).lower()

                # تحقق إذا كان هذا div يحتوي على اسم الأداة
                if query_lower in div_text:
                    # تحقق إذا كان يحتوي على رابط posts
                    post_link = div.find('a', href=lambda x: x and '/posts/' in x)
                    if post_link:
                        logger.info(f"✅ وجد نتيجة محتملة تحتوي على '{query}' مع رابط posts")
                        products.append(div)

                        # إذا وجدنا النتيجة الأولى، نتوقف (لأن Blitzit في النتيجة الأولى)
                        if len(products) >= 1:
                            break

            # Strategy 2: If no results found, search for all posts links
            if not products:
                logger.info("🔍 No direct results found, searching for all posts links...")
                post_links = soup.select('a[href*="/posts/"]')

                if post_links:
                    logger.info(f"✅ Found {len(post_links)} posts links")

                    # فحص كل رابط للعثور على المطابق
                    for link in post_links:
                        # فحص النص المحيط بالرابط
                        parent = link.parent
                        if parent:
                            parent_text = parent.get_text(strip=True).lower()
                            if query_lower in parent_text:
                                logger.info(f"✅ وجد رابط مطابق: {link.get('href', '')}")
                                products.append(parent)
                                break

                    # إذا لم نجد مطابق، خذ أول رابط (احتياطي)
                    if not products and post_links:
                        logger.info("⚠️ لم يوجد مطابق دقيق، أخذ أول رابط كاحتياطي")
                        first_link = post_links[0]
                        products.append(first_link.parent if first_link.parent else first_link)
                else:
                    logger.warning("❌ لم توجد أي روابط posts في صفحة البحث")

                    # استراتيجية 3: البحث المباشر عن الأداة
                    logger.info("🔍 محاولة البحث المباشر عن الأداة...")
                    direct_url = f"https://www.producthunt.com/posts/{query.lower()}"

                    try:
                        logger.info(f"🌐 محاولة الوصول المباشر: {direct_url}")
                        self.driver.get(direct_url)
                        time.sleep(5)

                        direct_html = self.driver.html
                        direct_soup = BeautifulSoup(direct_html, 'html.parser')
                        direct_title = direct_soup.title.get_text() if direct_soup.title else ""

                        # تحقق إذا كانت الصفحة المباشرة تحتوي على الأداة
                        if (query.lower() in direct_title.lower() and
                            'product hunt' in direct_title.lower() and
                            'not found' not in direct_title.lower()):
                            logger.info(f"✅ وجد الأداة عبر الرابط المباشر: {direct_title}")

                            # استخراج البيانات التفصيلية مباشرة
                            detailed_data = self.extract_detailed_producthunt_data(direct_soup, query, direct_url)
                            if detailed_data:
                                logger.info(f"✅ تم استخراج البيانات التفصيلية من الرابط المباشر")
                                return detailed_data
                        else:
                            logger.info(f"❌ الرابط المباشر لم ينجح: {direct_title}")

                    except Exception as e:
                        logger.warning(f"⚠️ خطأ في الرابط المباشر: {e}")

                    # استراتيجية 4: محاولة روابط بديلة
                    alternative_urls = [
                        f"https://www.producthunt.com/posts/{query.lower()}-2",
                        f"https://www.producthunt.com/posts/{query.lower()}-app",
                        f"https://www.producthunt.com/posts/{query.lower().replace(' ', '-')}"
                    ]

                    for alt_url in alternative_urls:
                        try:
                            logger.info(f"🔍 محاولة رابط بديل: {alt_url}")
                            self.driver.get(alt_url)
                            time.sleep(3)

                            alt_html = self.driver.html
                            alt_soup = BeautifulSoup(alt_html, 'html.parser')
                            alt_title = alt_soup.title.get_text() if alt_soup.title else ""

                            if (query.lower() in alt_title.lower() and
                                'product hunt' in alt_title.lower() and
                                'not found' not in alt_title.lower()):
                                logger.info(f"✅ وجد الأداة عبر الرابط البديل: {alt_title}")

                                detailed_data = self.extract_detailed_producthunt_data(alt_soup, query, alt_url)
                                if detailed_data:
                                    logger.info(f"✅ تم استخراج البيانات من الرابط البديل")
                                    return detailed_data
                        except Exception as e:
                            logger.warning(f"⚠️ خطأ في الرابط البديل {alt_url}: {e}")
                            continue

                    # Debug: تحليل هيكل الصفحة
                    logger.info("🔍 تحليل هيكل الصفحة للتشخيص:")
                    logger.info(f"   - العنوان: {soup.title.get_text() if soup.title else 'لا يوجد'}")
                    logger.info(f"   - عدد divs: {len(soup.select('div'))}")
                    logger.info(f"   - عدد links: {len(soup.select('a'))}")
                    logger.info(f"   - عدد h2: {len(soup.select('h2'))}")

                    # عرض جزء من محتوى الصفحة للتشخيص
                    page_text = soup.get_text()[:500]
                    logger.info(f"   - بداية محتوى الصفحة: {page_text}")

                    return None

            logger.info(f"✅ وجد {len(products)} نتائج للمعالجة")

            # معالجة النتائج الموجودة
            for i, product in enumerate(products[:3]):  # فحص أول 3 نتائج فقط
                try:
                    logger.info(f"🔍 معالجة النتيجة {i+1}/{min(len(products), 3)}...")

                    # استخراج اسم المنتج بناءً على الهيكل الفعلي
                    product_name = ""
                    product_url = ""

                    # البحث عن رابط المنتج أولاً
                    link_elem = product.find('a', href=lambda x: x and '/posts/' in x)
                    if link_elem:
                        href = link_elem.get('href', '')
                        if href:
                            # بناء URL كامل
                            if href.startswith('/'):
                                product_url = f"https://www.producthunt.com{href}"
                            else:
                                product_url = href

                            logger.info(f"🔗 وجد رابط المنتج: {product_url}")

                            # استخراج اسم المنتج من URL
                            url_parts = href.split('/')
                            if len(url_parts) > 2:
                                # URL مثل: /posts/blitzit-2
                                url_slug = url_parts[-1]
                                # إزالة الأرقام من النهاية
                                import re
                                clean_name = re.sub(r'-\d+$', '', url_slug)
                                product_name = clean_name.replace('-', ' ').title()
                                logger.info(f"✅ استخرج اسم المنتج من URL: {product_name}")

                    # إذا لم نجد اسم من URL، ابحث في النص
                    if not product_name:
                        logger.info("🔍 البحث عن اسم المنتج في النص...")

                        # البحث في عناصر مختلفة للعثور على الاسم
                        name_selectors = [
                            'h1', 'h2', 'h3',  # العناوين
                            'strong', 'b',     # النص المميز
                            'span', 'div'      # عناصر عامة
                        ]

                        for name_sel in name_selectors:
                            name_elems = product.select(name_sel)
                            for name_elem in name_elems:
                                text = name_elem.get_text(strip=True)
                                # تحقق إذا كان النص يحتوي على اسم الأداة المطلوبة
                                if (text and len(text) > 2 and len(text) < 50 and
                                    query.lower() in text.lower()):
                                    product_name = text
                                    logger.info(f"✅ وجد اسم المنتج في {name_sel}: {product_name}")
                                    break
                            if product_name:
                                break

                    # إذا لم نجد اسم محدد، استخدم اسم الاستعلام
                    if not product_name:
                        product_name = query
                        logger.info(f"⚠️ لم يوجد اسم محدد، استخدام اسم الاستعلام: {product_name}")

                    # تحقق من وجود URL
                    if not product_url:
                        logger.warning(f"⚠️ لم يوجد رابط للمنتج {product_name}")
                        continue

                    logger.info(f"🎯 معالجة المنتج: {product_name} - {product_url}")

                    # تحقق من المطابقة (مرن للغاية)
                    query_words = query.lower().split()
                    product_words = product_name.lower().split()

                    # مطابقة مرنة جداً
                    is_match = (
                        query.lower() in product_name.lower() or
                        product_name.lower() in query.lower() or
                        any(word in product_name.lower() for word in query_words if len(word) > 2) or
                        any(word in query.lower() for word in product_words if len(word) > 2)
                    )

                    # إذا كان هذا أول منتج ولم نجد مطابقة دقيقة، اعتبره مطابق (لأن Blitzit في النتيجة الأولى)
                    if i == 0 and not is_match:
                        logger.info(f"⚠️ أول نتيجة لا تطابق تماماً، لكن سنعتبرها مطابقة: {product_name}")
                        is_match = True

                    if is_match:
                        logger.info(f"✅ وجد منتج مطابق: {product_name}")

                        # الانتقال لصفحة المنتج للاستخراج التفصيلي
                        try:
                            logger.info(f"🌐 الانتقال لصفحة المنتج: {product_url}")
                            self.driver.get(product_url)
                            time.sleep(7)  # انتظار أطول لتحميل الصفحة

                            product_html = self.driver.html
                            product_soup = BeautifulSoup(product_html, 'html.parser')

                            # تحقق من تحميل الصفحة بشكل صحيح
                            page_title = product_soup.title.get_text() if product_soup.title else ""
                            logger.info(f"📄 عنوان صفحة المنتج: {page_title}")

                            # تحقق من وجود محتوى ProductHunt
                            if 'product hunt' in page_title.lower() or len(product_html) > 10000:
                                logger.info("✅ صفحة المنتج تحملت بنجاح")

                                # استخراج البيانات التفصيلية من صفحة المنتج
                                detailed_data = self.extract_detailed_producthunt_data(product_soup, product_name, product_url)
                                if detailed_data:
                                    logger.info(f"✅ تم استخراج البيانات التفصيلية لـ: {product_name}")
                                    return detailed_data
                                else:
                                    logger.warning(f"⚠️ فشل الاستخراج التفصيلي، محاولة احتياطية...")
                            else:
                                logger.warning(f"⚠️ صفحة المنتج لم تتحمل بشكل صحيح")

                        except Exception as e:
                            logger.warning(f"⚠️ خطأ في استخراج البيانات التفصيلية: {e}")
                            import traceback
                            logger.warning(f"⚠️ التفاصيل: {traceback.format_exc()}")
                    else:
                        logger.info(f"❌ المنتج لا يطابق الاستعلام: {product_name}")

                        # Fallback to basic data if detailed extraction fails
                        description = ""
                        desc_selectors = ['p', '.tagline', '[data-test*="tagline"]', '.description']
                        for desc_sel in desc_selectors:
                            desc_elem = product.select_one(desc_sel)
                            if desc_elem:
                                description = desc_elem.get_text(strip=True)
                                break

                        # Create basic tool data
                        tool_data = {
                            'company_name': product_name,
                            'short_description': description[:500] if description else f"{product_name} - AI tool found on ProductHunt",
                            'full_description': description,
                            'visit_website_url': product_url,
                            'detail_url': product_url,
                            'slug': product_name.lower().replace(' ', '-').replace('_', '-') if product_name else 'unknown',
                            'primary_task': 'Productivity',
                            'applicable_tasks': json.dumps(['Productivity', 'AI Tools', 'Software']),
                            'crawl_source': 'producthunt_web_basic',
                            'extraction_success': True,
                            'data_quality_score': 60  # Lower score for basic data
                        }

                        logger.info(f"✅ Created basic ProductHunt tool data for: {product_name}")
                        return tool_data

                except Exception as e:
                    logger.warning(f"⚠️ Error processing ProductHunt product: {e}")
                    continue

            logger.info("ℹ️ No matching products found in ProductHunt results")
            return None

        except Exception as e:
            logger.error(f"❌ ProductHunt web search error: {e}")
            return None

    def search_web(self, query: str, tool_url: str = None) -> Optional[Dict]:
        """Search tool using web search engines"""
        logger.info("🔍 Checking web search APIs...")

        # Try Google Search first
        if self.google_search_key and self.google_search_engine_id:
            logger.info("🔍 Trying Google Search...")
            try:
                result = self.google_search(query, tool_url)
                if result:
                    return result
            except Exception as e:
                logger.warning(f"⚠️ Google Search failed: {e}")
        else:
            logger.info("ℹ️ Google Search API not configured")

        # Try Bing Search as fallback
        if self.bing_search_key:
            logger.info("🔍 Trying Bing Search...")
            try:
                result = self.bing_search(query, tool_url)
                if result:
                    return result
            except Exception as e:
                logger.warning(f"⚠️ Bing Search failed: {e}")
        else:
            logger.info("ℹ️ Bing Search API not configured")

        # Try simple web search without API
        logger.info("🔍 Trying simple web search...")
        result = self.simple_web_search(query, tool_url)
        if result:
            return result

        logger.warning("⚠️ No web search results found")
        return None

    def google_search(self, query: str, tool_url: str = None) -> Optional[Dict]:
        """Search using Google Custom Search API"""
        try:
            search_query = f"{query} AI tool"
            if tool_url:
                search_query += f" site:{urlparse(tool_url).netloc}"

            params = {
                'key': self.google_search_key,
                'cx': self.google_search_engine_id,
                'q': search_query,
                'num': self.max_search_results
            }

            response = requests.get(
                'https://www.googleapis.com/customsearch/v1',
                params=params,
                timeout=self.search_timeout
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])

                if items:
                    # Process first relevant result
                    for item in items:
                        tool_data = self.extract_tool_from_search_result(item)
                        if tool_data:
                            return tool_data

        except Exception as e:
            logger.error(f"❌ Google search error: {e}")

        return None

    def simple_web_search(self, query: str, tool_url: str = None) -> Optional[Dict]:
        """Simple web search without API - create basic tool data"""
        try:
            logger.info(f"🔍 Creating basic tool data for: {query}")

            # If we have a URL, try to extract data from it
            if tool_url:
                logger.info(f"🌐 Extracting data from provided URL: {tool_url}")
                enhanced_data = self.fetch_and_extract_tool_page(tool_url)
                if enhanced_data:
                    enhanced_data.update({
                        'company_name': query,
                        'visit_website_url': tool_url,
                        'crawl_source': 'simple_web_search',
                        'extraction_success': True,
                        'data_quality_score': 70
                    })
                    return enhanced_data

            # Create basic tool data
            basic_tool_data = {
                'company_name': query,
                'short_description': f"{query} - AI tool",
                'visit_website_url': tool_url or f"https://{query.lower().replace(' ', '')}.com",
                'slug': query.lower().replace(' ', '-').replace('_', '-'),  # Add slug
                'detail_url': tool_url or f"https://{query.lower().replace(' ', '')}.com",
                'crawl_source': 'simple_web_search',
                'extraction_success': True,
                'data_quality_score': 50  # Low quality without real data
            }

            logger.info(f"✅ Created basic tool data for: {query}")
            return basic_tool_data

        except Exception as e:
            logger.error(f"❌ Simple web search error: {e}")
            return None

    def bing_search(self, query: str, tool_url: str = None) -> Optional[Dict]:
        """Search using Bing Search API"""
        try:
            search_query = f"{query} AI tool"
            if tool_url:
                search_query += f" site:{urlparse(tool_url).netloc}"

            headers = {
                'Ocp-Apim-Subscription-Key': self.bing_search_key
            }

            params = {
                'q': search_query,
                'count': self.max_search_results,
                'mkt': 'en-US'
            }

            response = requests.get(
                'https://api.bing.microsoft.com/v7.0/search',
                headers=headers,
                params=params,
                timeout=self.search_timeout
            )

            if response.status_code == 200:
                data = response.json()
                web_pages = data.get('webPages', {}).get('value', [])

                if web_pages:
                    # Process first relevant result
                    for page in web_pages:
                        tool_data = self.extract_tool_from_search_result(page)
                        if tool_data:
                            return tool_data

        except Exception as e:
            logger.error(f"❌ Bing search error: {e}")

        return None

    def extract_tool_from_search_result(self, result: Dict) -> Optional[Dict]:
        """Extract tool data from search result"""
        try:
            title = result.get('title', '')
            snippet = result.get('snippet', result.get('description', ''))
            url = result.get('link', result.get('url', ''))

            # Basic tool data structure
            tool_name = self.clean_tool_name(title)
            tool_data = {
                'company_name': tool_name,
                'short_description': snippet[:500] if snippet else '',
                'visit_website_url': url,
                'detail_url': url,
                'slug': tool_name.lower().replace(' ', '-').replace('_', '-') if tool_name else 'unknown',  # Add slug
                'crawl_source': 'web_search',
                'extraction_success': True,
                'data_quality_score': 60  # Medium quality from search
            }

            # Try to extract more data by fetching the page
            enhanced_data = self.fetch_and_extract_tool_page(url)
            if enhanced_data:
                tool_data.update(enhanced_data)
                tool_data['data_quality_score'] = 80

            return tool_data

        except Exception as e:
            logger.error(f"❌ Error extracting tool from search result: {e}")
            return None

    def fetch_and_extract_tool_page(self, url: str) -> Optional[Dict]:
        """Fetch and extract tool data from webpage"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=15)
            if response.status_code != 200:
                return None

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract basic information
            extracted_data = {}

            # Try to find logo
            logo_selectors = [
                'img[alt*="logo"]', 'img[class*="logo"]',
                '.logo img', '#logo img', 'header img'
            ]
            for selector in logo_selectors:
                logo = soup.select_one(selector)
                if logo and logo.get('src'):
                    extracted_data['logo_url'] = urljoin(url, logo['src'])
                    break

            # Try to find description
            desc_selectors = [
                'meta[name="description"]', 'meta[property="og:description"]',
                '.description', '#description', 'p'
            ]
            for selector in desc_selectors:
                desc = soup.select_one(selector)
                if desc:
                    if desc.name == 'meta':
                        description = desc.get('content', '')
                    else:
                        description = desc.get_text(strip=True)

                    if description and len(description) > 50:
                        extracted_data['full_description'] = description[:1000]
                        break

            # Try to find pricing info
            pricing_keywords = ['free', 'paid', 'premium', 'freemium', 'subscription', 'price', '$']
            page_text = soup.get_text().lower()
            for keyword in pricing_keywords:
                if keyword in page_text:
                    # Extract pricing context
                    pricing_context = self.extract_pricing_context(page_text, keyword)
                    if pricing_context:
                        extracted_data['pricing'] = pricing_context
                        break

            return extracted_data

        except Exception as e:
            logger.error(f"❌ Error fetching tool page {url}: {e}")
            return None

    def extract_detailed_producthunt_data(self, soup: BeautifulSoup, product_name: str, product_url: str) -> Optional[Dict]:
        """Extract comprehensive detailed data from ProductHunt product page"""
        try:
            logger.info(f"🔍 بدء الاستخراج التفصيلي الشامل من ProductHunt لـ: {product_name}")

            # المرحلة 1: استخراج البيانات الأساسية
            logger.info("📊 المرحلة 1: استخراج البيانات الأساسية...")
            basic_data = self.extract_basic_producthunt_data(soup, product_name, product_url)

            # Stage 2: Extract reviews and ratings
            logger.info("📊 Stage 2: Extracting reviews and ratings...")
            reviews_data = self.extract_producthunt_reviews(soup)

            # Stage 2.5: Extract maker comments (official tool owner information)
            logger.info("📊 Stage 2.5: Extracting maker comments...")
            maker_comments = self.extract_maker_comments(soup, product_name)

            # Stage 2.6: Extract all comments from the page for FAQ generation
            logger.info("📊 Stage 2.6: Extracting all comments for FAQ generation...")
            all_comments = self.extract_all_comments_from_page(soup)

            # المرحلة 3: استخراج معلومات التسعير والميزات
            logger.info("📊 المرحلة 3: استخراج معلومات التسعير والميزات...")
            pricing_features_data = self.extract_producthunt_pricing_features(soup)

            # Stage 4: Merge all data including maker comments and all comments
            logger.info("📊 Stage 4: Merging all data...")
            comprehensive_data = self.merge_producthunt_data(basic_data, reviews_data, pricing_features_data, maker_comments, all_comments)

            # المرحلة 5: اكتشاف البيانات الناقصة واستكمالها
            logger.info("📊 المرحلة 5: اكتشاف البيانات الناقصة واستكمالها...")
            complete_data = self.complete_missing_producthunt_data(comprehensive_data, soup)

            # المرحلة 6: استخدام الذكاء الاصطناعي لاستنتاج البيانات المفقودة
            logger.info("📊 المرحلة 6: استخدام الذكاء الاصطناعي لاستنتاج البيانات المفقودة...")
            final_data = self.ai_enhance_producthunt_data(complete_data, soup)

            # المرحلة 7: التحقق النهائي وضمان اكتمال جميع الحقول
            logger.info("📊 المرحلة 7: التحقق النهائي وضمان اكتمال جميع الحقول...")
            verified_data = self.verify_and_complete_all_fields(final_data)

            logger.info(f"✅ اكتمل الاستخراج التفصيلي الشامل: جودة {verified_data.get('data_quality_score', 0)}%")
            return verified_data

        except Exception as e:
            logger.error(f"❌ خطأ في الاستخراج التفصيلي الشامل: {e}")
            return None

    def extract_basic_producthunt_data(self, soup: BeautifulSoup, product_name: str, product_url: str) -> Dict:
        """استخراج البيانات الأساسية من صفحة ProductHunt"""
        try:
            logger.info("🔍 استخراج البيانات الأساسية...")

            # استخراج الوصف/العنوان الفرعي مع selectors محدثة حسب الهيكل الفعلي
            description = ""
            full_description = ""

            # Debug: تحليل هيكل الصفحة
            logger.info(f"🔍 تحليل صفحة ProductHunt...")
            page_title = soup.title.get_text() if soup.title else "No title"
            logger.info(f"📄 عنوان الصفحة: {page_title}")

            # استخراج الوصف القصير (short_description) - حسب الهيكل الفعلي
            short_desc_selectors = [
                'h2.text-18.text-gray-700',  # الهيكل الفعلي المرسل
                'h2[class*="text-18"]',
                'h2[class*="text-gray-700"]',
                'h2.text-gray-700',
                'h2',  # fallback
                'meta[name="description"]',
                'meta[property="og:description"]'
            ]

            for selector in short_desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    if desc_elem.name == 'meta':
                        description = desc_elem.get('content', '')
                    else:
                        description = desc_elem.get_text(strip=True)

                    if description and len(description) > 10:
                        logger.info(f"✅ وجد الوصف القصير عبر {selector}: {description}")
                        break

            # استخراج الوصف الكامل (full_description) - حسب الهيكل الفعلي
            full_desc_selectors = [
                'div[data-sentry-component="Description"] p.prose',  # الهيكل الفعلي المرسل
                'div[data-sentry-component="Description"] p',
                'p.prose.text-16',
                'p[class*="prose"]',
                '.prose',
                'div[data-sentry-component="Description"]'
            ]

            for selector in full_desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    full_description = desc_elem.get_text(strip=True)
                    if full_description and len(full_description) > 20:
                        logger.info(f"✅ وجد الوصف الكامل عبر {selector}: {full_description[:100]}...")
                        break

            # Use full description as fallback for short description
            if not description and full_description:
                description = full_description[:500]  # Truncate for short description
                logger.info(f"✅ استخدم الوصف الكامل كوصف قصير")

            # If still no description, try generic selectors
            if not description:
                logger.info("🔍 البحث عن وصف في عناصر عامة...")
                generic_selectors = ['h2', 'h3', 'p']
                for selector in generic_selectors:
                    desc_elems = soup.select(selector)
                    for desc_elem in desc_elems:
                        text = desc_elem.get_text(strip=True)
                        if (text and len(text) > 20 and len(text) < 500 and
                            any(keyword in text.lower() for keyword in ['to-do', 'timer', 'productivity', 'simple', 'app', 'tool'])):
                            description = text
                            logger.info(f"✅ وجد وصف من عنصر عام: {description[:100]}...")
                            break
                    if description:
                        break

            # استخراج رابط الموقع الرسمي حسب الهيكل الفعلي
            website_url = ""

            # البحث عن span يحتوي على اسم الموقع (حسب الهيكل المرسل)
            website_span_selectors = [
                'span[class*="max-w"][class*="truncate"][class*="font-semibold"]',  # الهيكل الفعلي المرسل (مصحح)
                'span[class*="truncate"][class*="font-semibold"]',
                'span[class*="max-w"]',
                'span.font-semibold',
                'span[class*="font-semibold"]'
            ]

            website_domain = ""
            for selector in website_span_selectors:
                span_elem = soup.select_one(selector)
                if span_elem:
                    website_domain = span_elem.get_text(strip=True)
                    if website_domain and '.' in website_domain:
                        # Convert domain to full URL
                        if not website_domain.startswith('http'):
                            website_url = f"https://{website_domain}"
                        else:
                            website_url = website_domain
                        logger.info(f"✅ وجد رابط الموقع من span: {website_url}")
                        break

            # Fallback: البحث عن روابط خارجية
            if not website_url:
                logger.info("🔍 البحث عن روابط خارجية...")
                excluded_domains = [
                    'producthunt.com', 'twitter.com', 'facebook.com', 'linkedin.com',
                    'instagram.com', 'youtube.com', 'github.com', 'discord.com'
                ]

                external_link_selectors = [
                    'a[href*="http"]:not([href*="producthunt.com"])',
                    'a[target="_blank"]',
                    'a[rel*="external"]'
                ]

                for selector in external_link_selectors:
                    link_elems = soup.select(selector)
                    for link_elem in link_elems:
                        href = link_elem.get('href', '')
                        if href and href.startswith('http'):
                            is_excluded = any(domain in href.lower() for domain in excluded_domains)
                            if not is_excluded:
                                website_url = href
                                logger.info(f"✅ وجد رابط خارجي: {website_url}")
                                break
                    if website_url:
                        break

            # Final fallback: البحث في النص
            if not website_url:
                logger.info("🔍 البحث عن رابط في النص...")
                import re
                page_text = soup.get_text()
                # Look for domain patterns
                domain_pattern = r'\b[a-zA-Z0-9-]+\.[a-zA-Z]{2,}\b'
                domains = re.findall(domain_pattern, page_text)
                for domain in domains:
                    if (domain not in ['producthunt.com', 'twitter.com', 'facebook.com'] and
                        len(domain) > 5 and '.' in domain):
                        website_url = f"https://{domain}"
                        logger.info(f"✅ وجد رابط من النص: {website_url}")
                        break

            # استخراج الشعار (logo_url) حسب الهيكل الفعلي مع معالجة srcset
            logo_url = ""
            logo_selectors = [
                'img[data-test*="thumbnail"]',  # حسب الهيكل المرسل
                'img[class*="rounded"][class*="rounded-xl"]',
                'img[alt="' + product_name + '"]',  # البحث بالاسم المحدد
                'img[data-test="product-avatar"]',
                'img[class*="product-avatar"]',
                'img[alt*="logo"]',
                'meta[property="og:image"]'
            ]

            for selector in logo_selectors:
                logo_elem = soup.select_one(selector)
                if logo_elem:
                    if logo_elem.name == 'meta':
                        logo_url = logo_elem.get('content', '')
                    else:
                        # معالجة srcset أولاً (الأولوية للجودة العالية)
                        srcset = logo_elem.get('srcset', '')
                        src = logo_elem.get('src', '')

                        if srcset:
                            # استخراج أول URL من srcset (عادة الجودة الأساسية)
                            srcset_urls = srcset.split(',')
                            if srcset_urls:
                                # أخذ أول URL وتنظيفه
                                first_url = srcset_urls[0].strip()
                                # إزالة الجزء الخاص بـ DPR (1x, 2x, 3x)
                                logo_url = first_url.split(' ')[0]
                                logger.info(f"✅ وجد الشعار من srcset عبر {selector}: {logo_url}")
                        elif src:
                            logo_url = src
                            logger.info(f"✅ وجد الشعار من src عبر {selector}: {logo_url}")

                    if logo_url:
                        break

            # استخراج الصورة المميزة (featured_image_url) حسب الهيكل الفعلي مع معالجة srcset
            featured_image_url = ""
            featured_selectors = [
                'section[data-sentry-component="ProductGallery"] img',  # حسب الهيكل المرسل
                'div[data-sentry-component="Gallery"] img',
                'section.overflow-hidden img',
                'img[alt*="gallery"]',
                'img[loading="eager"]',  # الصورة الأولى عادة
                'img[class*="rounded-xl"]'
            ]

            for selector in featured_selectors:
                img_elem = soup.select_one(selector)
                if img_elem:
                    # معالجة srcset أولاً (نفس طريقة الشعار)
                    srcset = img_elem.get('srcset', '')
                    src = img_elem.get('src', '')

                    if srcset:
                        # استخراج أول URL من srcset
                        srcset_urls = srcset.split(',')
                        if srcset_urls:
                            first_url = srcset_urls[0].strip()
                            featured_image_url = first_url.split(' ')[0]
                            logger.info(f"✅ وجد الصورة المميزة من srcset عبر {selector}: {featured_image_url}")
                    elif src:
                        featured_image_url = src
                        logger.info(f"✅ وجد الصورة المميزة من src عبر {selector}: {featured_image_url}")

                    # تأكد أنها ليست نفس الشعار
                    if featured_image_url and featured_image_url != logo_url:
                        break
                    else:
                        featured_image_url = ""  # إعادة تعيين إذا كانت نفس الشعار

            # استخراج الفئات/المواضيع حسب الهيكل الفعلي
            topics = []

            # حسب الهيكل المرسل: div.flex.flex-row.flex-wrap.items-center.gap-2 > a
            topic_selectors = [
                'div[data-sentry-component="Categories"] a',  # حسب الهيكل المرسل
                'div.flex.flex-row.flex-wrap a',
                'a[href*="/categories/"]',  # روابط الفئات
                'a[class*="whitespace-nowrap"][class*="rounded-full"]',
                'a[href*="/topics/"]',
                '.category a',
                '.topic'
            ]

            for selector in topic_selectors:
                topic_elems = soup.select(selector)
                for elem in topic_elems:
                    topic = elem.get_text(strip=True)
                    # تنظيف النص (إزالة "software" و "apps" الزائدة)
                    topic = topic.replace(' software', '').replace(' apps', '').strip()
                    if topic and topic not in topics and len(topic) < 50 and len(topic) > 2:
                        topics.append(topic)

            logger.info(f"✅ وجد {len(topics)} فئات: {topics}")

            # إنشاء البيانات الأساسية المحسنة
            basic_data = {
                'company_name': product_name,
                'short_description': description[:500] if description else f"{product_name} - AI tool from ProductHunt",
                'full_description': full_description or description,
                'visit_website_url': website_url or product_url,
                'detail_url': product_url,
                'logo_url': logo_url,
                'featured_image_url': featured_image_url,  # إضافة الصورة المميزة
                'slug': product_name.lower().replace(' ', '-').replace('_', '-'),
                'primary_task': topics[0] if topics else 'Productivity',
                'applicable_tasks': topics[:5] if topics else ['Productivity', 'AI Tools'],
                'crawl_source': 'producthunt_detailed_basic_enhanced',
                'extraction_success': True,
                'data_quality_score': 70  # درجة أعلى للبيانات المحسنة
            }

            logger.info("✅ اكتمل استخراج البيانات الأساسية")
            return basic_data

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج البيانات الأساسية: {e}")
            return {
                'company_name': product_name,
                'short_description': f"{product_name} - AI tool from ProductHunt",
                'visit_website_url': product_url,
                'detail_url': product_url,
                'slug': product_name.lower().replace(' ', '-').replace('_', '-'),
                'crawl_source': 'producthunt_basic_fallback',
                'extraction_success': False,
                'data_quality_score': 30
            }

    def extract_producthunt_reviews(self, soup: BeautifulSoup) -> Dict:
        """استخراج المراجعات والتقييمات من ProductHunt"""
        try:
            logger.info("🔍 استخراج المراجعات والتقييمات...")

            reviews_data = {
                'pros': [],
                'cons': [],
                'user_feedback': [],
                'rating_info': {},
                'review_count': 0
            }

            # استخراج المراجعات والتعليقات
            review_selectors = [
                '.comment',
                '.review',
                '[data-test="comment"]',
                '.user-comment',
                '.feedback'
            ]

            all_reviews = []
            for selector in review_selectors:
                review_elems = soup.select(selector)
                for elem in review_elems:
                    review_text = elem.get_text(strip=True)
                    if review_text and len(review_text) > 20:
                        all_reviews.append(review_text)

            logger.info(f"✅ وجد {len(all_reviews)} مراجعة")

            # تحليل المراجعات لاستخراج المزايا والعيوب
            if all_reviews:
                pros, cons = self.analyze_reviews_for_pros_cons(all_reviews)
                reviews_data['pros'] = pros
                reviews_data['cons'] = cons
                reviews_data['user_feedback'] = all_reviews[:10]  # أول 10 مراجعات
                reviews_data['review_count'] = len(all_reviews)

            # استخراج معلومات التقييم
            rating_selectors = [
                '.rating',
                '.score',
                '[data-test="rating"]',
                '.stars'
            ]

            for selector in rating_selectors:
                rating_elem = soup.select_one(selector)
                if rating_elem:
                    rating_text = rating_elem.get_text(strip=True)
                    if rating_text:
                        reviews_data['rating_info']['text'] = rating_text
                        break

            # استخراج عدد الأصوات/الإعجابات
            vote_selectors = [
                '[data-test="vote-count"]',
                '.vote-count',
                '.upvotes',
                '.likes'
            ]

            for selector in vote_selectors:
                vote_elem = soup.select_one(selector)
                if vote_elem:
                    vote_text = vote_elem.get_text(strip=True)
                    if vote_text:
                        reviews_data['rating_info']['votes'] = vote_text
                        break

            logger.info(f"✅ اكتمل استخراج المراجعات: {len(reviews_data['pros'])} مزايا، {len(reviews_data['cons'])} عيوب")
            return reviews_data

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج المراجعات: {e}")
            return {
                'pros': [],
                'cons': [],
                'user_feedback': [],
                'rating_info': {},
                'review_count': 0
            }

    def analyze_reviews_for_pros_cons(self, reviews: list) -> tuple:
        """تحليل المراجعات لاستخراج المزايا والعيوب"""
        try:
            logger.info("🔍 تحليل المراجعات لاستخراج المزايا والعيوب...")

            pros = []
            cons = []

            # كلمات مفتاحية للمزايا
            positive_keywords = [
                'great', 'excellent', 'amazing', 'fantastic', 'wonderful', 'perfect',
                'love', 'best', 'awesome', 'brilliant', 'outstanding', 'impressive',
                'helpful', 'useful', 'easy', 'simple', 'fast', 'quick', 'efficient',
                'good', 'nice', 'cool', 'smart', 'innovative', 'powerful'
            ]

            # كلمات مفتاحية للعيوب
            negative_keywords = [
                'bad', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'dislike',
                'slow', 'difficult', 'hard', 'complex', 'confusing', 'buggy',
                'expensive', 'costly', 'limited', 'lacking', 'missing', 'poor',
                'disappointing', 'frustrating', 'annoying', 'useless'
            ]

            for review in reviews[:20]:  # تحليل أول 20 مراجعة
                review_lower = review.lower()

                # البحث عن المزايا
                for keyword in positive_keywords:
                    if keyword in review_lower:
                        # استخراج الجملة التي تحتوي على الكلمة المفتاحية
                        sentences = review.split('.')
                        for sentence in sentences:
                            if keyword in sentence.lower() and len(sentence.strip()) > 10:
                                clean_sentence = sentence.strip()
                                if clean_sentence not in pros and len(pros) < 10:
                                    pros.append(clean_sentence)
                                break
                        break

                # البحث عن العيوب
                for keyword in negative_keywords:
                    if keyword in review_lower:
                        sentences = review.split('.')
                        for sentence in sentences:
                            if keyword in sentence.lower() and len(sentence.strip()) > 10:
                                clean_sentence = sentence.strip()
                                if clean_sentence not in cons and len(cons) < 10:
                                    cons.append(clean_sentence)
                                break
                        break

            logger.info(f"✅ تم تحليل المراجعات: {len(pros)} مزايا، {len(cons)} عيوب")
            return pros, cons

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل المراجعات: {e}")
            return [], []

    def extract_producthunt_pricing_features(self, soup: BeautifulSoup) -> Dict:
        """استخراج معلومات التسعير والميزات من ProductHunt"""
        try:
            logger.info("🔍 استخراج معلومات التسعير والميزات...")

            pricing_features_data = {
                'pricing': '',
                'features': [],
                'pricing_model': '',
                'free_tier': False,
                'trial_available': False
            }

            # استخراج معلومات التسعير
            pricing_selectors = [
                '.pricing',
                '.price',
                '[data-test="pricing"]',
                '.cost',
                '.subscription',
                'span:contains("$")',
                'div:contains("free")',
                'div:contains("paid")',
                'div:contains("premium")'
            ]

            pricing_text = ""
            for selector in pricing_selectors:
                pricing_elems = soup.select(selector)
                for elem in pricing_elems:
                    text = elem.get_text(strip=True).lower()
                    if any(keyword in text for keyword in ['$', 'free', 'paid', 'premium', 'subscription', 'month', 'year']):
                        pricing_text += text + " "

            if pricing_text:
                pricing_features_data['pricing'] = pricing_text.strip()[:200]

                # تحديد نموذج التسعير
                if 'free' in pricing_text:
                    pricing_features_data['free_tier'] = True
                    if 'paid' in pricing_text or '$' in pricing_text:
                        pricing_features_data['pricing_model'] = 'Freemium'
                    else:
                        pricing_features_data['pricing_model'] = 'Free'
                elif '$' in pricing_text or 'paid' in pricing_text:
                    pricing_features_data['pricing_model'] = 'Paid'
                elif 'trial' in pricing_text:
                    pricing_features_data['trial_available'] = True
                    pricing_features_data['pricing_model'] = 'Trial Available'

            # استخراج الميزات
            feature_selectors = [
                '.feature',
                '.benefit',
                '[data-test="feature"]',
                '.capability',
                'li',
                '.highlight'
            ]

            features = []
            for selector in feature_selectors:
                feature_elems = soup.select(selector)
                for elem in feature_elems:
                    feature_text = elem.get_text(strip=True)
                    if feature_text and len(feature_text) > 5 and len(feature_text) < 100:
                        if feature_text not in features and len(features) < 15:
                            features.append(feature_text)

            pricing_features_data['features'] = features

            logger.info(f"✅ اكتمل استخراج التسعير والميزات: {len(features)} ميزة، نموذج: {pricing_features_data['pricing_model']}")
            return pricing_features_data

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج التسعير والميزات: {e}")
            return {
                'pricing': '',
                'features': [],
                'pricing_model': '',
                'free_tier': False,
                'trial_available': False
            }

    def extract_maker_comments(self, soup: BeautifulSoup, product_name: str) -> Dict:
        """Extract official comments from tool maker/owner"""
        try:
            logger.info("🔍 Extracting maker comments...")

            maker_data = {
                'official_description': '',
                'features': [],
                'additional_info': '',
                'maker_name': ''
            }

            # Look for maker comments with the specific structure provided
            maker_selectors = [
                # Main maker comment structure
                'div[data-sentry-component="CommentByLine"]',
                # Alternative selectors for maker identification
                'div:has(.bg-success-400)',  # Green "Maker" badge
                'div:has(svg):has(.text-white)',  # Maker badge with icon
            ]

            maker_comments = []

            for selector in maker_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        # Check if this is a maker comment (has the green "Maker" badge)
                        maker_badge = element.select_one('.bg-success-400')
                        if maker_badge and 'Maker' in maker_badge.get_text():
                            # Found a maker comment
                            logger.info("✅ Found maker comment")

                            # Extract maker name
                            name_elem = element.select_one('a[class*="font-semibold"]')
                            if name_elem:
                                maker_data['maker_name'] = name_elem.get_text(strip=True)

                            # Find the actual comment content (usually in a sibling or parent element)
                            comment_content = self.find_comment_content(element)
                            if comment_content:
                                maker_comments.append(comment_content)
                                logger.info(f"✅ Extracted maker comment: {comment_content[:100]}...")

                except Exception as e:
                    logger.warning(f"⚠️ Error with selector {selector}: {e}")
                    continue

            # Process maker comments to extract useful information
            if maker_comments:
                # Combine all maker comments
                full_comment = ' '.join(maker_comments)

                # Extract official description (usually the longest comment)
                longest_comment = max(maker_comments, key=len) if maker_comments else ''
                if len(longest_comment) > 50:
                    maker_data['official_description'] = longest_comment

                # Extract features mentioned by maker
                features = self.extract_features_from_text(full_comment)
                if features:
                    maker_data['features'] = features

                # Store additional info
                maker_data['additional_info'] = full_comment[:500]  # First 500 chars

                logger.info(f"✅ Processed {len(maker_comments)} maker comments")
            else:
                logger.info("ℹ️ No maker comments found")

            return maker_data

        except Exception as e:
            logger.error(f"❌ Error extracting maker comments: {e}")
            return {
                'official_description': '',
                'features': [],
                'additional_info': '',
                'maker_name': ''
            }

    def find_comment_content(self, maker_element) -> str:
        """Find the actual comment content near a maker badge"""
        try:
            # Look for comment content in various locations relative to the maker badge

            # Strategy 1: Look in parent elements
            parent = maker_element.parent
            for _ in range(3):  # Check up to 3 levels up
                if parent:
                    # Look for comment text in paragraphs or divs
                    comment_elems = parent.select('p, div[class*="comment"], div[class*="text"]')
                    for elem in comment_elems:
                        text = elem.get_text(strip=True)
                        if len(text) > 20 and not any(skip in text.lower() for skip in ['maker', 'badge', 'icon']):
                            return text
                    parent = parent.parent
                else:
                    break

            # Strategy 2: Look in sibling elements
            siblings = maker_element.find_next_siblings()
            for sibling in siblings[:5]:  # Check next 5 siblings
                if sibling.name in ['p', 'div']:
                    text = sibling.get_text(strip=True)
                    if len(text) > 20:
                        return text

            # Strategy 3: Look in following elements
            next_elem = maker_element.find_next()
            for _ in range(10):  # Check next 10 elements
                if next_elem:
                    if next_elem.name in ['p', 'div'] and len(next_elem.get_text(strip=True)) > 20:
                        return next_elem.get_text(strip=True)
                    next_elem = next_elem.find_next()
                else:
                    break

            return ""

        except Exception as e:
            logger.warning(f"⚠️ Error finding comment content: {e}")
            return ""

    def extract_features_from_text(self, text: str) -> list:
        """Extract features mentioned in maker comments"""
        try:
            features = []

            # Common feature indicators
            feature_patterns = [
                r'features?[:\s]+([^.!?]+)',
                r'includes?[:\s]+([^.!?]+)',
                r'supports?[:\s]+([^.!?]+)',
                r'offers?[:\s]+([^.!?]+)',
                r'provides?[:\s]+([^.!?]+)',
            ]

            import re
            for pattern in feature_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    # Split by common separators
                    feature_parts = re.split(r'[,;]', match)
                    for part in feature_parts:
                        clean_feature = part.strip()
                        if len(clean_feature) > 5 and len(clean_feature) < 100:
                            features.append(clean_feature)

            # Remove duplicates and return first 5
            unique_features = list(dict.fromkeys(features))
            return unique_features[:5]

        except Exception as e:
            logger.warning(f"⚠️ Error extracting features from text: {e}")
            return []

    def extract_features_from_maker_text(self, text: str) -> list:
        """Extract specific features from maker's detailed description"""
        try:
            features = []

            # Look for specific feature mentions in maker's text
            feature_indicators = [
                # Direct feature mentions
                r'⏱️\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Timer features
                r'🗓️\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Calendar features
                r'✅\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Task features
                r'🔌\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Integration features
                r'🗒️\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Note features
                r'🔗\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Link features
                r'🚨\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Alert features
                r'📊\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Report features
                r'🌓\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Theme features
                r'⌨️\s*([^⏱️🗓️✅🔌🗒️🔗🚨📊🌓⌨️]+)',  # Keyboard features

                # Text-based feature patterns
                r'(?:includes?|offers?|provides?|features?)[:\s]*([^.!?\n]+)',
                r'(?:you can|able to|allows?)[:\s]*([^.!?\n]+)',
                r'(?:with|using|via)[:\s]*([^.!?\n]+)',
            ]

            import re
            for pattern in feature_indicators:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    # Clean and process the match
                    clean_match = re.sub(r'[^\w\s-]', '', match).strip()
                    if len(clean_match) > 10 and len(clean_match) < 80:
                        # Split by common separators
                        parts = re.split(r'(?:and|or|,|;|\n)', clean_match)
                        for part in parts:
                            clean_part = part.strip()
                            if len(clean_part) > 5 and len(clean_part) < 60:
                                features.append(clean_part)

            # Also look for specific productivity features mentioned
            productivity_keywords = [
                'pomodoro timer', 'task scheduling', 'subtasks', 'notion sync',
                'google calendar', 'quick notes', 'auto open links', 'anti-distraction',
                'productivity reports', 'keyboard shortcuts', 'focus mode', 'time tracking',
                'break reminders', 'task prioritization', 'workflow automation'
            ]

            text_lower = text.lower()
            for keyword in productivity_keywords:
                if keyword in text_lower:
                    features.append(keyword.title())

            # Remove duplicates and return first 8
            unique_features = list(dict.fromkeys(features))
            return unique_features[:8]

        except Exception as e:
            logger.warning(f"⚠️ Error extracting features from maker text: {e}")
            return []

    def extract_all_comments_from_page(self, soup: BeautifulSoup) -> list:
        """Extract all comments from ProductHunt page for FAQ generation"""
        try:
            logger.info("🔍 Extracting all comments from page...")

            comments = []

            # Look for comments feed
            comments_feed = soup.select('div[data-test="comments-feed"]')
            if not comments_feed:
                logger.info("ℹ️ No comments feed found")
                return []

            # Extract individual comments
            comment_elements = soup.select('div[data-test^="comment-"]')

            for comment_elem in comment_elements:
                try:
                    # Extract comment text
                    comment_text_elem = comment_elem.select_one('div[data-sentry-component="RichText"] div.prose')
                    if comment_text_elem:
                        comment_text = comment_text_elem.get_text(strip=True)

                        # Extract commenter name
                        name_elem = comment_elem.select_one('a[class*="font-semibold"]')
                        commenter_name = name_elem.get_text(strip=True) if name_elem else "Anonymous"

                        # Check if this is a maker comment
                        is_maker = bool(comment_elem.select_one('.bg-success-400'))

                        if comment_text and len(comment_text) > 20:
                            comments.append({
                                'text': comment_text,
                                'author': commenter_name,
                                'is_maker': is_maker
                            })
                            logger.info(f"✅ Extracted comment from {commenter_name}: {comment_text[:50]}...")

                except Exception as e:
                    logger.warning(f"⚠️ Error extracting individual comment: {e}")
                    continue

            logger.info(f"✅ Extracted {len(comments)} comments from page")
            return comments

        except Exception as e:
            logger.error(f"❌ Error extracting comments from page: {e}")
            return []

    def extract_cons_from_maker_text(self, text: str) -> list:
        """Extract potential limitations/cons from maker's description"""
        try:
            cons = []

            # Look for mentions of limitations, future features, or areas for improvement
            limitation_patterns = [
                r'(?:currently|only)\s+available\s+on\s+([^.!?\n]+)',  # Platform limitations
                r'(?:not yet|coming soon|on the horizon|planned)[:\s]*([^.!?\n]+)',  # Future features (implies current limitations)
                r'(?:beta|early|limited)[:\s]*([^.!?\n]+)',  # Beta/limited features
                r'(?:requires?|needs?)[:\s]*([^.!?\n]+)',  # Requirements (potential barriers)
                r'(?:discount|deal|limited time)[:\s]*([^.!?\n]+)',  # Pricing limitations
            ]

            import re
            for pattern in limitation_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    clean_match = re.sub(r'[^\w\s-]', '', match).strip()
                    if len(clean_match) > 5 and len(clean_match) < 80:
                        # Convert to limitation statement
                        if 'available on' in clean_match.lower():
                            cons.append(f"Currently limited to {clean_match}")
                        elif any(word in clean_match.lower() for word in ['coming', 'planned', 'horizon']):
                            cons.append(f"Missing features: {clean_match}")
                        elif 'beta' in clean_match.lower():
                            cons.append("Still in beta version")
                        elif 'requires' in clean_match.lower():
                            cons.append(f"Requires {clean_match}")

            # Look for specific platform limitations mentioned
            if 'windows & macos' in text.lower() and 'ios and android' in text.lower():
                cons.append("Mobile apps not yet available")

            if 'lifetime deal' in text.lower() or 'discount' in text.lower():
                cons.append("Pricing may increase after promotional period")

            if 'beta' in text.lower():
                cons.append("Still in beta development phase")

            # Remove duplicates and return first 3
            unique_cons = list(dict.fromkeys(cons))
            return unique_cons[:3]

        except Exception as e:
            logger.warning(f"⚠️ Error extracting cons from maker text: {e}")
            return []

    def extract_pricing_from_maker_text(self, text: str) -> str:
        """Extract pricing information from maker's description"""
        try:
            # Look for pricing patterns in maker's text
            pricing_patterns = [
                r'lifetime deal[:\s]*([^.!?\n]+)',
                r'discount[:\s]*([^.!?\n]+)',
                r'(?:use code|code)[:\s]*([^.!?\n]+)',
                r'(\d+%\s*off)',
                r'(\$\d+(?:\.\d{2})?)',
                r'(free[^.!?\n]*)',
                r'(freemium[^.!?\n]*)',
                r'(subscription[^.!?\n]*)',
            ]

            import re
            pricing_info = []

            for pattern in pricing_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    clean_match = match.strip()
                    if len(clean_match) > 2 and len(clean_match) < 100:
                        pricing_info.append(clean_match)

            # Look for specific pricing mentions
            text_lower = text.lower()

            if 'lifetime deal' in text_lower and '30% off' in text_lower:
                return "Lifetime deal available with 30% discount"
            elif 'lifetime deal' in text_lower:
                return "Lifetime deal available"
            elif 'discount' in text_lower and any(word in text_lower for word in ['30%', 'off']):
                return "Discounted pricing available"
            elif 'free' in text_lower and 'premium' in text_lower:
                return "Freemium model"
            elif 'free' in text_lower:
                return "Free version available"
            elif any(word in text_lower for word in ['$', 'subscription', 'paid']):
                return "Paid subscription model"

            # If specific patterns found, combine them
            if pricing_info:
                return " - ".join(pricing_info[:2])  # Combine first 2 pieces of info

            return ""

        except Exception as e:
            logger.warning(f"⚠️ Error extracting pricing from maker text: {e}")
            return ""

    def merge_producthunt_data(self, basic_data: Dict, reviews_data: Dict, pricing_features_data: Dict, maker_comments: Dict = None, all_comments: list = None) -> Dict:
        """Merge all extracted data from ProductHunt including maker comments"""
        try:
            logger.info("🔍 Merging all extracted data...")

            # Merge basic data
            merged_data = basic_data.copy()

            # Add reviews data
            merged_data['pros'] = reviews_data.get('pros', [])
            merged_data['cons'] = reviews_data.get('cons', [])
            merged_data['user_feedback'] = reviews_data.get('user_feedback', [])
            merged_data['review_count'] = reviews_data.get('review_count', 0)

            # Add pricing and features data
            merged_data['pricing'] = pricing_features_data.get('pricing', '')
            merged_data['features'] = pricing_features_data.get('features', [])
            merged_data['pricing_model'] = pricing_features_data.get('pricing_model', '')
            merged_data['free_tier'] = pricing_features_data.get('free_tier', False)
            merged_data['trial_available'] = pricing_features_data.get('trial_available', False)

            # Add maker comments data for AI processing
            if maker_comments:
                # Store maker comments for AI analysis (don't extract manually)
                if maker_comments.get('official_description'):
                    merged_data['maker_comments'] = maker_comments['official_description']
                    logger.info("✅ Stored maker comments for AI analysis")

                if maker_comments.get('maker_name'):
                    merged_data['maker_name'] = maker_comments['maker_name']
                if maker_comments.get('additional_info'):
                    merged_data['maker_additional_info'] = maker_comments['additional_info']

            # Add all comments for FAQ generation
            if all_comments:
                merged_data['all_comments'] = all_comments
                logger.info(f"✅ Stored {len(all_comments)} comments for FAQ generation")

            # تحديث درجة الجودة بناءً على اكتمال البيانات
            quality_score = 60  # البداية

            if merged_data.get('full_description') and len(merged_data['full_description']) > 50:
                quality_score += 10
            if merged_data.get('pros') and len(merged_data['pros']) > 0:
                quality_score += 10
            if merged_data.get('cons') and len(merged_data['cons']) > 0:
                quality_score += 5
            if merged_data.get('pricing'):
                quality_score += 10
            if merged_data.get('features') and len(merged_data['features']) > 0:
                quality_score += 5

            merged_data['data_quality_score'] = min(quality_score, 100)
            merged_data['crawl_source'] = 'producthunt_detailed_merged'

            logger.info(f"✅ Data merging completed: quality {merged_data['data_quality_score']}%")
            return merged_data

        except Exception as e:
            logger.error(f"❌ Error merging data: {e}")
            return basic_data

    def complete_missing_producthunt_data(self, data: Dict, soup: BeautifulSoup) -> Dict:
        """اكتشاف البيانات الناقصة واستكمالها من الصفحة"""
        try:
            logger.info("🔍 اكتشاف البيانات الناقصة واستكمالها...")

            completed_data = data.copy()
            missing_fields = []

            # فحص الحقول المطلوبة
            required_fields = {
                'short_description': 'وصف قصير',
                'full_description': 'وصف كامل',
                'pricing': 'تسعير',
                'pros': 'مزايا',
                'cons': 'عيوب',
                'logo_url': 'شعار',
                'featured_image_url': 'صورة مميزة'
            }

            for field, description in required_fields.items():
                if not completed_data.get(field) or (isinstance(completed_data[field], list) and len(completed_data[field]) == 0):
                    missing_fields.append((field, description))

            logger.info(f"📊 الحقول الناقصة: {[desc for _, desc in missing_fields]}")

            # استكمال الحقول الناقصة
            for field, description in missing_fields:
                logger.info(f"🔍 محاولة استكمال: {description}")

                if field == 'short_description' and not completed_data.get('short_description'):
                    # البحث عن وصف قصير في أماكن أخرى
                    desc_selectors = [
                        'meta[name="twitter:description"]',
                        'meta[property="og:description"]',
                        '.subtitle',
                        '.summary',
                        'p:first-of-type'
                    ]

                    for selector in desc_selectors:
                        elem = soup.select_one(selector)
                        if elem:
                            if elem.name == 'meta':
                                desc = elem.get('content', '')
                            else:
                                desc = elem.get_text(strip=True)
                            if desc and len(desc) > 20:
                                completed_data['short_description'] = desc[:500]
                                logger.info(f"✅ وجد وصف قصير: {desc[:100]}...")
                                break

                elif field == 'featured_image_url' and not completed_data.get('featured_image_url'):
                    # البحث عن صورة مميزة
                    img_selectors = [
                        'meta[property="og:image"]',
                        'meta[name="twitter:image"]',
                        '.hero-image img',
                        '.featured-image img',
                        '.product-screenshot img'
                    ]

                    for selector in img_selectors:
                        elem = soup.select_one(selector)
                        if elem:
                            if elem.name == 'meta':
                                img_url = elem.get('content', '')
                            else:
                                img_url = elem.get('src', '')
                            if img_url:
                                completed_data['featured_image_url'] = img_url
                                logger.info(f"✅ وجد صورة مميزة: {img_url}")
                                break

                elif field == 'pros' and (not completed_data.get('pros') or len(completed_data['pros']) == 0):
                    # Skip automatic pros extraction - let AI handle it with proper context
                    logger.info("ℹ️ Pros field empty - will be handled by AI enhancement with proper context")
                    pass

                elif field == 'cons' and (not completed_data.get('cons') or len(completed_data['cons']) == 0):
                    # البحث عن عيوب في النص أو إنشاء عيوب عامة
                    cons = self.extract_cons_from_text(soup)
                    if not cons:
                        # إنشاء عيوب عامة بناءً على نوع الأداة
                        cons = self.generate_generic_cons(completed_data)
                    completed_data['cons'] = cons
                    logger.info(f"✅ وجد/أنشأ {len(cons)} عيوب")

                elif field == 'pricing' and not completed_data.get('pricing'):
                    # البحث عن معلومات التسعير في النص
                    pricing = self.extract_pricing_from_text(soup)
                    if pricing:
                        completed_data['pricing'] = pricing
                        logger.info(f"✅ وجد تسعير: {pricing}")

            # تحديث درجة الجودة
            original_missing = len(missing_fields)
            current_missing = 0
            for field, _ in missing_fields:
                if not completed_data.get(field) or (isinstance(completed_data[field], list) and len(completed_data[field]) == 0):
                    current_missing += 1

            improvement = original_missing - current_missing
            if improvement > 0:
                completed_data['data_quality_score'] = min(completed_data.get('data_quality_score', 60) + (improvement * 5), 100)

            logger.info(f"✅ اكتمل استكمال البيانات: تحسن {improvement} حقول، جودة {completed_data['data_quality_score']}%")
            return completed_data

        except Exception as e:
            logger.error(f"❌ خطأ في استكمال البيانات الناقصة: {e}")
            return data

    def extract_pros_from_text(self, soup: BeautifulSoup) -> list:
        """Extract real pros from page content"""
        try:
            pros = []

            # Extract actual sentences that describe benefits/features
            all_text = soup.get_text()

            # Look for sentences that describe features or benefits
            sentences = all_text.split('.')

            # Keywords that indicate positive features
            positive_indicators = [
                'helps', 'enables', 'allows', 'provides', 'offers', 'includes',
                'supports', 'features', 'delivers', 'ensures', 'improves',
                'enhances', 'streamlines', 'simplifies', 'automates', 'integrates'
            ]

            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 20 and len(sentence) < 150:  # Reasonable length
                    sentence_lower = sentence.lower()

                    # Check if sentence describes a feature/benefit
                    for indicator in positive_indicators:
                        if indicator in sentence_lower:
                            # Clean and format the sentence
                            clean_sentence = sentence.strip()
                            if clean_sentence and clean_sentence not in pros:
                                pros.append(clean_sentence)
                                break

                    if len(pros) >= 5:
                        break

            # If no specific pros found, return empty list (let AI handle it)
            return pros[:5] if pros else []

        except Exception as e:
            logger.error(f"❌ Error extracting pros from text: {e}")
            return []

    def extract_cons_from_text(self, soup: BeautifulSoup) -> list:
        """استخراج العيوب من النص العام للصفحة"""
        try:
            cons = []

            # البحث عن كلمات سلبية في النص
            negative_phrases = [
                'expensive', 'costly', 'limited', 'complex', 'difficult',
                'slow', 'buggy', 'unstable', 'lacking', 'missing'
            ]

            all_text = soup.get_text().lower()

            for phrase in negative_phrases:
                if phrase in all_text:
                    cons.append(f"May have {phrase} aspects")
                    if len(cons) >= 3:
                        break

            return cons[:3]

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج العيوب من النص: {e}")
            return []

    def generate_generic_cons(self, data: Dict) -> list:
        """إنشاء عيوب عامة بناءً على نوع الأداة"""
        try:
            cons = []

            # عيوب عامة للأدوات المدفوعة
            if data.get('pricing_model') == 'Paid' or '$' in str(data.get('pricing', '')):
                cons.append("Requires paid subscription for full features")

            # عيوب عامة للأدوات المعقدة
            if 'AI' in str(data.get('primary_task', '')) or 'artificial intelligence' in str(data.get('full_description', '')).lower():
                cons.append("May require learning curve for optimal use")
                cons.append("Results quality depends on input quality")

            # عيوب عامة للأدوات الجديدة
            cons.append("Limited user reviews and feedback available")

            return cons[:3]

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء العيوب العامة: {e}")
            return ["Limited information available for comprehensive evaluation"]

    def extract_pricing_from_text(self, soup: BeautifulSoup) -> str:
        """استخراج معلومات التسعير من النص"""
        try:
            all_text = soup.get_text()

            # البحث عن أنماط التسعير
            pricing_patterns = [
                r'\$\d+(?:\.\d{2})?(?:/month|/mo|/year|/yr)?',
                r'free(?:\s+(?:tier|plan|version))?',
                r'freemium',
                r'subscription',
                r'one-time payment',
                r'pay-per-use'
            ]

            import re
            for pattern in pricing_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                if matches:
                    return matches[0]

            # البحث عن كلمات التسعير العامة
            if 'free' in all_text.lower():
                return "Free tier available"
            elif '$' in all_text:
                return "Paid service with various pricing options"
            else:
                return "Contact for pricing"

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج التسعير من النص: {e}")
            return "Pricing information not available"

    def ai_enhance_producthunt_data(self, data: Dict, soup: BeautifulSoup) -> Dict:
        """استخدام الذكاء الاصطناعي لاستنتاج البيانات المفقودة"""
        try:
            logger.info("🤖 استخدام الذكاء الاصطناعي لاستنتاج البيانات المفقودة...")

            enhanced_data = data.copy()

            # الخطوة 1: تحليل موقع الأداة الفعلي
            website_url = enhanced_data.get('visit_website_url', '')
            logger.info(f"🔍 فحص رابط الموقع: {website_url}")

            if website_url and website_url.startswith('http'):
                logger.info(f"🌐 بدء تحليل موقع الأداة: {website_url}")
                website_analysis = self.analyze_tool_website_with_ai(website_url, enhanced_data)
                if website_analysis:
                    # دمج البيانات المحسنة من الموقع
                    logger.info(f"📊 البيانات المستخرجة من الموقع: {list(website_analysis.keys())}")
                    for key, value in website_analysis.items():
                        if value and (not enhanced_data.get(key) or enhanced_data[key] in ['Unknown', 'Contact for pricing', 'Pricing information not available']):
                            enhanced_data[key] = value
                            logger.info(f"✅ تم تحديث {key}: {str(value)[:50]}...")
                    logger.info("✅ تم تحسين البيانات من تحليل الموقع")
                else:
                    logger.warning("⚠️ لم يتم استخراج بيانات من تحليل الموقع")
            else:
                logger.warning(f"⚠️ رابط الموقع غير صالح أو مفقود: {website_url}")

            # Step 2: Check fields that need AI enhancement using maker comments
            ai_enhancement_needed = []

            if not enhanced_data.get('pros') or len(enhanced_data['pros']) < 3:
                ai_enhancement_needed.append('pros')
            if not enhanced_data.get('cons') or len(enhanced_data['cons']) < 2:
                ai_enhancement_needed.append('cons')
            if not enhanced_data.get('faqs') or len(enhanced_data.get('faqs', [])) < 3:
                ai_enhancement_needed.append('faqs')
            if not enhanced_data.get('full_description') or len(enhanced_data['full_description']) < 100:
                ai_enhancement_needed.append('full_description')

            if not ai_enhancement_needed:
                logger.info("✅ No AI enhancement needed - data is complete")
                return enhanced_data

            logger.info(f"📊 Fields needing AI enhancement: {ai_enhancement_needed}")

            # Prepare context for AI including maker comments
            context_text = self.prepare_ai_context_with_maker_comments(enhanced_data, soup)

            # Try all AI models and choose best results
            best_results = {}

            for field in ai_enhancement_needed:
                logger.info(f"🤖 Enhancing field: {field}")
                field_results = self.try_all_ai_models_for_field(field, enhanced_data, context_text)

                if field_results:
                    best_results[field] = field_results
                    logger.info(f"✅ Successfully enhanced {field}")
                else:
                    logger.warning(f"⚠️ Failed to enhance {field}")

            # Apply best results
            for field, result in best_results.items():
                enhanced_data[field] = result

            # Update quality score
            if best_results:
                improvement = len(best_results) * 5
                enhanced_data['data_quality_score'] = min(enhanced_data.get('data_quality_score', 60) + improvement, 100)
                enhanced_data['ai_enhanced'] = True
                enhanced_data['ai_enhanced_fields'] = list(best_results.keys())

            logger.info(f"✅ AI enhancement completed: improved {len(best_results)} fields, quality {enhanced_data['data_quality_score']}%")
            return enhanced_data

        except Exception as e:
            logger.error(f"❌ خطأ في التحسين بالذكاء الاصطناعي: {e}")
            return data

    def analyze_tool_website_with_ai(self, website_url: str, tool_data: Dict) -> Optional[Dict]:
        """تحليل موقع الأداة الفعلي باستخدام الذكاء الاصطناعي لاستخراج معلومات دقيقة"""
        try:
            logger.info(f"🌐 بدء تحليل موقع الأداة: {website_url}")

            # جلب محتوى الموقع
            website_content = self.fetch_website_content_for_analysis(website_url)
            if not website_content:
                logger.warning("⚠️ فشل في جلب محتوى الموقع")
                return None

            # إعداد prompt للتحليل
            analysis_prompt = self.create_website_analysis_prompt(tool_data, website_content)

            # تجريب نماذج الذكاء الاصطناعي للتحليل
            analysis_result = self.get_best_ai_analysis(analysis_prompt)

            if analysis_result:
                # معالجة نتيجة التحليل
                processed_analysis = self.process_website_analysis_result(analysis_result)
                logger.info("✅ تم تحليل الموقع بنجاح")
                return processed_analysis
            else:
                logger.warning("⚠️ فشل في تحليل الموقع")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل موقع الأداة: {e}")
            return None

    def fetch_website_content_for_analysis(self, url: str) -> Optional[str]:
        """جلب محتوى الموقع للتحليل"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=15)
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.content, 'html.parser')

                # إزالة العناصر غير المرغوب فيها
                for element in soup(['script', 'style', 'nav', 'footer', 'header']):
                    element.decompose()

                # استخراج النص المفيد
                text_content = soup.get_text()

                # تنظيف النص
                lines = [line.strip() for line in text_content.split('\n') if line.strip()]
                clean_content = '\n'.join(lines)

                # تحديد الطول للتحليل (أول 3000 حرف)
                return clean_content[:3000]
            else:
                logger.warning(f"⚠️ فشل في جلب الموقع: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في جلب محتوى الموقع: {e}")
            return None

    def create_website_analysis_prompt(self, tool_data: Dict, website_content: str) -> str:
        """Create prompt for analyzing tool website"""
        tool_name = tool_data.get('company_name', 'Unknown')

        return f"""Analyze the website of {tool_name} and extract the following information accurately:

Current tool information:
- Name: {tool_name}
- Description: {tool_data.get('short_description', 'Not available')}

Website content:
{website_content}

Extract from the website (only existing information, do not invent):

1. ACCURATE PRICING INFORMATION:
   - Is the tool free?
   - What are the subscription plans?
   - Detailed prices?
   - Free trial information?
   - Lifetime deals or discounts?

2. Main features (from website only):
   - What features are mentioned on the website?

3. Additional information:
   - Logo URL if found
   - Featured image URLs

Please respond in JSON format only:
{{
  "pricing": "Accurate pricing information",
  "features": ["Feature 1", "Feature 2", "Feature 3"],
  "logo_url": "Logo URL if found",
  "additional_info": "Useful additional information"
}}

JSON only:"""

    def get_best_ai_analysis(self, prompt: str) -> Optional[str]:
        """الحصول على أفضل تحليل من نماذج الذكاء الاصطناعي"""
        try:
            # قائمة النماذج المتاحة (أولوية للأفضل)
            models_to_try = []

            if self.llm_keys.get('gemini'):
                models_to_try.append(('gemini', 'Gemini'))
            if self.llm_keys.get('openai'):
                models_to_try.append(('openai', 'OpenAI'))
            if self.llm_keys.get('anthropic'):
                models_to_try.append(('anthropic', 'Claude'))
            if self.llm_keys.get('deepseek'):
                models_to_try.append(('deepseek', 'DeepSeek'))

            for model_type, model_name in models_to_try:
                try:
                    logger.info(f"🤖 تجريب {model_name} لتحليل الموقع...")

                    result = None
                    if model_type == 'gemini':
                        result = self.call_gemini(prompt, self.llm_keys.get('gemini', ''))
                    elif model_type == 'openai':
                        result = self.call_openai(prompt, self.llm_keys.get('openai', ''))
                    elif model_type == 'anthropic':
                        result = self.call_anthropic(prompt, self.llm_keys.get('anthropic', ''))
                    elif model_type == 'deepseek':
                        result = self.call_deepseek(prompt, self.llm_keys.get('deepseek', ''))

                    if result and len(result.strip()) > 20:
                        logger.info(f"✅ {model_name} أنتج تحليل جيد")
                        return result
                    else:
                        logger.warning(f"⚠️ {model_name} لم ينتج تحليل مفيد")

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في {model_name}: {e}")
                    continue

            logger.warning("❌ فشل جميع النماذج في تحليل الموقع")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تحليل الذكاء الاصطناعي: {e}")
            return None

    def process_website_analysis_result(self, analysis_result: str) -> Dict:
        """معالجة نتيجة تحليل الموقع"""
        try:
            import json

            # محاولة استخراج JSON من النتيجة
            result_text = analysis_result.strip()

            # البحث عن JSON في النص
            start_idx = result_text.find('{')
            end_idx = result_text.rfind('}') + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = result_text[start_idx:end_idx]
                try:
                    parsed_result = json.loads(json_str)

                    # Clean and improve results
                    processed = {}

                    if parsed_result.get('pricing'):
                        pricing_info = str(parsed_result['pricing']).strip()
                        # Clean pricing information
                        if pricing_info and pricing_info.lower() not in ['unknown', 'not available', 'n/a']:
                            processed['pricing'] = pricing_info
                            logger.info(f"✅ Extracted pricing from website: {pricing_info}")

                    if parsed_result.get('features') and isinstance(parsed_result['features'], list):
                        features = [str(f).strip() for f in parsed_result['features'][:5] if str(f).strip()]
                        if features:
                            processed['pros'] = features
                            logger.info(f"✅ Extracted {len(features)} features from website")

                    if parsed_result.get('logo_url'):
                        logo_url = str(parsed_result['logo_url']).strip()
                        if logo_url and logo_url.startswith('http'):
                            processed['logo_url'] = logo_url

                    if parsed_result.get('additional_info'):
                        additional = str(parsed_result['additional_info']).strip()
                        if len(additional) > 50:
                            processed['additional_info'] = additional[:500]

                    return processed

                except json.JSONDecodeError:
                    logger.warning("⚠️ فشل في تحليل JSON، محاولة استخراج يدوي")

            # استخراج يدوي إذا فشل JSON
            manual_extraction = {}

            # البحث عن معلومات التسعير
            pricing_keywords = ['free', 'paid', 'premium', 'price', '$', '€', '£', 'subscription', 'plan']
            for keyword in pricing_keywords:
                if keyword.lower() in result_text.lower():
                    # استخراج الجملة التي تحتوي على الكلمة
                    sentences = result_text.split('.')
                    for sentence in sentences:
                        if keyword.lower() in sentence.lower():
                            manual_extraction['pricing'] = sentence.strip()[:100]
                            break
                    break

            return manual_extraction

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتيجة تحليل الموقع: {e}")
            return {}

    def prepare_ai_context(self, data: Dict, soup: BeautifulSoup) -> str:
        """Prepare context for AI"""
        try:
            context_parts = []

            # Basic tool information
            context_parts.append(f"Tool Name: {data.get('company_name', 'Unknown')}")

            if data.get('short_description'):
                context_parts.append(f"Description: {data['short_description']}")

            if data.get('primary_task'):
                context_parts.append(f"Primary Task: {data['primary_task']}")

            if data.get('applicable_tasks'):
                tasks = data['applicable_tasks'] if isinstance(data['applicable_tasks'], list) else []
                if tasks:
                    context_parts.append(f"Categories: {', '.join(tasks[:5])}")

            # Extract additional text from page
            page_text = soup.get_text()[:2000]  # First 2000 chars
            context_parts.append(f"Page Content: {page_text}")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"❌ Error preparing AI context: {e}")
            return f"Tool: {data.get('company_name', 'Unknown')}"

    def prepare_ai_context_with_maker_comments(self, data: Dict, soup: BeautifulSoup) -> str:
        """Prepare enhanced context for AI including maker comments"""
        try:
            context_parts = []

            # Basic tool information
            context_parts.append(f"Tool Name: {data.get('company_name', 'Unknown')}")

            if data.get('short_description'):
                context_parts.append(f"Short Description: {data['short_description']}")

            if data.get('primary_task'):
                context_parts.append(f"Primary Task: {data['primary_task']}")

            if data.get('applicable_tasks'):
                tasks = data['applicable_tasks'] if isinstance(data['applicable_tasks'], list) else []
                if tasks:
                    context_parts.append(f"Categories: {', '.join(tasks[:5])}")

            # Add maker comments (most important for AI analysis)
            if data.get('maker_comments'):
                context_parts.append(f"Official Maker Description: {data['maker_comments']}")

            # Add all comments for FAQ generation
            if data.get('all_comments'):
                comments_text = []
                for comment in data['all_comments'][:10]:  # Limit to first 10 comments
                    comment_info = f"{comment['author']}: {comment['text'][:200]}"
                    comments_text.append(comment_info)
                if comments_text:
                    context_parts.append(f"User Comments: {' | '.join(comments_text)}")

            # Add existing pros/cons if any
            if data.get('pros'):
                pros = data['pros'] if isinstance(data['pros'], list) else []
                if pros:
                    context_parts.append(f"Current Pros: {', '.join(pros[:3])}")

            if data.get('cons'):
                cons = data['cons'] if isinstance(data['cons'], list) else []
                if cons:
                    context_parts.append(f"Current Cons: {', '.join(cons[:3])}")

            # Add website URL for reference
            if data.get('visit_website_url'):
                context_parts.append(f"Website: {data['visit_website_url']}")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"❌ Error preparing enhanced AI context: {e}")
            return f"Tool: {data.get('company_name', 'Unknown')}"

    def try_all_ai_models_for_field(self, field: str, data: Dict, context: str) -> Optional[any]:
        """تجريب جميع نماذج الذكاء الاصطناعي لحقل معين واختيار أفضل نتيجة"""
        try:
            logger.info(f"🤖 تجريب جميع النماذج لحقل: {field}")

            # إعداد الطلب حسب نوع الحقل
            prompt = self.create_field_specific_prompt(field, data, context)

            # قائمة النماذج المتاحة (Gemini أولاً كما طلب المستخدم)
            available_models = []

            if self.llm_keys.get('gemini'):
                available_models.append(('gemini', 'Gemini'))
            if self.llm_keys.get('openai'):
                available_models.append(('openai', 'OpenAI GPT'))
            if self.llm_keys.get('anthropic'):
                available_models.append(('anthropic', 'Claude'))
            if self.llm_keys.get('deepseek'):
                available_models.append(('deepseek', 'DeepSeek'))
            if self.llm_keys.get('mistral'):
                available_models.append(('mistral', 'Mistral'))
            if self.llm_keys.get('groq'):
                available_models.append(('groq', 'Groq'))

            # إضافة Ollama إذا كان متاحاً
            available_models.append(('ollama', 'Ollama Local'))

            if not available_models:
                logger.warning("⚠️ لا توجد نماذج ذكاء اصطناعي متاحة")
                return None

            logger.info(f"📊 النماذج المتاحة: {[name for _, name in available_models]}")

            # تجريب كل نموذج
            results = {}

            for model_type, model_name in available_models:
                try:
                    logger.info(f"🔄 تجريب {model_name}...")

                    result = None
                    if model_type == 'gemini':
                        result = self.call_gemini(prompt, self.llm_keys.get('gemini', ''))
                    elif model_type == 'openai':
                        result = self.call_openai(prompt, self.llm_keys.get('openai', ''))
                    elif model_type == 'anthropic':
                        result = self.call_anthropic(prompt, self.llm_keys.get('anthropic', ''))
                    elif model_type == 'deepseek':
                        result = self.call_deepseek(prompt, self.llm_keys.get('deepseek', ''))
                    elif model_type == 'mistral':
                        result = self.call_mistral(prompt, self.llm_keys.get('mistral', ''))
                    elif model_type == 'groq':
                        result = self.call_groq(prompt, self.llm_keys.get('groq', ''))
                    elif model_type == 'ollama':
                        result = self.call_ollama(prompt, "")

                    if result:
                        processed_result = self.process_ai_result(result, field)
                        if processed_result:
                            results[model_name] = processed_result
                            logger.info(f"✅ {model_name} أنتج نتيجة جيدة")
                        else:
                            logger.warning(f"⚠️ {model_name} أنتج نتيجة غير صالحة")
                    else:
                        logger.warning(f"⚠️ {model_name} لم ينتج نتيجة")

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في {model_name}: {e}")
                    continue

            # اختيار أفضل نتيجة
            if results:
                best_result = self.select_best_ai_result(results, field)
                logger.info(f"✅ أفضل نتيجة من: {list(results.keys())}")
                return best_result
            else:
                logger.warning(f"❌ لم تنتج أي نماذج نتائج صالحة لحقل: {field}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في تجريب النماذج لحقل {field}: {e}")
            return None

    def create_field_specific_prompt(self, field: str, data: Dict, context: str) -> str:
        """Create field-specific prompt for AI analysis"""
        try:
            tool_name = data.get('company_name', 'Unknown Tool')

            if field == 'pros':
                return f"""Based on the following information about {tool_name}, extract and identify the SPECIFIC advantages and benefits mentioned in the maker's description.

{context}

From the maker's description and tool information, identify the ACTUAL benefits and features mentioned. Focus on:
- Specific features mentioned by the maker
- Actual capabilities described
- Real benefits stated in the description
- Integration capabilities mentioned
- Unique selling points highlighted

DO NOT create generic statements. Extract only what is actually mentioned or clearly implied from the content.

Please provide only a JSON array of 3-5 specific advantages based on the actual content:
["Specific advantage from description", "Actual feature mentioned", "Real benefit stated"]

Response (JSON array only):"""

            elif field == 'cons':
                return f"""Based on the following information about {tool_name}, analyze and infer potential disadvantages/limitations in English.

{context}

From the maker's description, infer realistic limitations such as:
- Platform availability constraints
- Feature limitations mentioned
- Pricing constraints
- Development stage limitations

Please provide only a JSON array of 2-3 realistic limitations:
["Limitation 1", "Limitation 2"]

Response (JSON array only):"""

            elif field == 'faqs':
                return f"""Based on the following information about {tool_name}, generate relevant FAQ questions and answers in English.

{context}

Analyze the maker's description and user comments to create 3-5 frequently asked questions that users would have about this tool. Focus on:
- How it works (based on maker's description)
- Pricing and plans (extract from maker's description)
- Features and capabilities (from maker's description)
- Integrations (mentioned in maker's description)
- Platform availability (mentioned in maker's description)
- Common concerns or questions from user comments

Use the user comments to understand what people are asking about or discussing regarding this tool.

Please provide only a JSON array of objects with "question" and "answer" fields:
[{{"question": "How does it work?", "answer": "Brief answer based on the description and comments"}}, ...]

Response (JSON array only):"""

            elif field == 'full_description':
                return f"""Based on the following information about {tool_name}, create a concise description (100-200 words) in English:

{context}

Summarize the key points from the maker's description into a clear, concise overview. Focus on:
- What the tool does
- Main features
- Target users
- Key benefits

Please provide only the description text, no additional formatting:"""

            else:
                return f"Analyze {tool_name} and provide information about {field}: {context}"

        except Exception as e:
            logger.error(f"❌ Error creating prompt for field {field}: {e}")
            return f"Analyze the tool and provide {field} information."

    def process_ai_result(self, result: str, field: str) -> Optional[any]:
        """Process AI result based on field type"""
        try:
            if not result or len(result.strip()) < 5:
                return None

            result = result.strip()

            if field in ['pros', 'cons']:
                # Try to parse JSON
                try:
                    import json
                    # Find JSON array in text
                    start = result.find('[')
                    end = result.rfind(']') + 1
                    if start >= 0 and end > start:
                        json_str = result[start:end]
                        parsed = json.loads(json_str)
                        if isinstance(parsed, list) and len(parsed) > 0:
                            return [str(item).strip() for item in parsed if str(item).strip()]
                except:
                    pass

                # If JSON fails, parse plain text
                lines = [line.strip() for line in result.split('\n') if line.strip()]
                items = []
                for line in lines:
                    # Remove symbols and numbers from beginning
                    clean_line = line.lstrip('- •*1234567890. ').strip()
                    if clean_line and len(clean_line) > 10:
                        items.append(clean_line)

                return items[:5] if field == 'pros' else items[:3]

            elif field == 'faqs':
                # Try to parse JSON for FAQs
                try:
                    import json
                    # Find JSON array in text
                    start = result.find('[')
                    end = result.rfind(']') + 1
                    if start >= 0 and end > start:
                        json_str = result[start:end]
                        parsed = json.loads(json_str)
                        if isinstance(parsed, list) and len(parsed) > 0:
                            # Validate FAQ structure
                            valid_faqs = []
                            for item in parsed:
                                if isinstance(item, dict) and 'question' in item and 'answer' in item:
                                    valid_faqs.append({
                                        'question': str(item['question']).strip(),
                                        'answer': str(item['answer']).strip()
                                    })
                            return valid_faqs[:5] if valid_faqs else None
                except Exception as e:
                    logger.warning(f"⚠️ Error parsing FAQs JSON: {e}")
                    pass

                # If JSON fails, try to extract Q&A from text
                try:
                    faqs = []
                    lines = result.split('\n')
                    current_q = None
                    current_a = None

                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue

                        # Look for question patterns
                        if any(pattern in line.lower() for pattern in ['question:', 'q:', '?']):
                            if current_q and current_a:
                                faqs.append({'question': current_q, 'answer': current_a})
                            current_q = line.replace('Question:', '').replace('Q:', '').strip()
                            current_a = None
                        # Look for answer patterns
                        elif any(pattern in line.lower() for pattern in ['answer:', 'a:']):
                            current_a = line.replace('Answer:', '').replace('A:', '').strip()
                        elif current_q and not current_a:
                            current_a = line

                    # Add last Q&A if exists
                    if current_q and current_a:
                        faqs.append({'question': current_q, 'answer': current_a})

                    return faqs[:5] if faqs else None
                except:
                    pass

                return None

            elif field in ['full_description', 'pricing']:
                # Clean text
                clean_result = result.replace('\n', ' ').strip()
                if len(clean_result) > 10:
                    return clean_result[:500] if field == 'full_description' else clean_result[:200]

            return None

        except Exception as e:
            logger.error(f"❌ Error processing AI result for field {field}: {e}")
            return None

    def select_best_ai_result(self, results: Dict, field: str) -> any:
        """Select best result from AI models"""
        try:
            if not results:
                return None

            # If only one result
            if len(results) == 1:
                return list(results.values())[0]

            # Evaluate results by quality
            scored_results = []

            for model_name, result in results.items():
                score = 0

                if field in ['pros', 'cons']:
                    if isinstance(result, list):
                        score += len(result) * 10  # Points for count
                        score += sum(len(item) for item in result)  # Points for length

                elif field == 'faqs':
                    if isinstance(result, list):
                        score += len(result) * 15  # Higher points for FAQs
                        for faq in result:
                            if isinstance(faq, dict) and 'question' in faq and 'answer' in faq:
                                score += len(faq['question']) + len(faq['answer'])  # Points for content length

                elif field in ['full_description', 'pricing']:
                    if isinstance(result, str):
                        score += len(result)  # Points for length
                        score += result.count(' ') * 2  # Points for word count

                scored_results.append((score, model_name, result))

            # Sort by score and choose best
            scored_results.sort(reverse=True)
            best_score, best_model, best_result = scored_results[0]

            logger.info(f"✅ Best result from {best_model} with score {best_score}")
            return best_result

        except Exception as e:
            logger.error(f"❌ Error selecting best result: {e}")
            return list(results.values())[0] if results else None

    def verify_and_complete_all_fields(self, data: Dict) -> Dict:
        """التحقق النهائي وضمان اكتمال جميع الحقول المطلوبة"""
        try:
            logger.info("🔍 التحقق النهائي وضمان اكتمال جميع الحقول...")

            verified_data = data.copy()

            # الحقول المطلوبة مع القيم الافتراضية
            required_fields = {
                'company_name': verified_data.get('company_name', 'Unknown Tool'),
                'short_description': verified_data.get('short_description') or f"{verified_data.get('company_name', 'Unknown Tool')} - AI tool",
                'full_description': verified_data.get('full_description') or verified_data.get('short_description') or f"Comprehensive AI tool for productivity and automation",
                'primary_task': verified_data.get('primary_task', 'Productivity'),
                'applicable_tasks': verified_data.get('applicable_tasks', ['Productivity', 'AI Tools']),
                'pros': verified_data.get('pros', []),  # Don't use generic pros, let AI generate specific ones
                'cons': verified_data.get('cons', ['Learning curve required', 'Limited free features']),
                'pricing': verified_data.get('pricing', 'Contact for pricing'),
                'featured_image_url': verified_data.get('featured_image_url', ''),
                'visit_website_url': verified_data.get('visit_website_url', verified_data.get('detail_url', '')),
                'logo_url': verified_data.get('logo_url', ''),
                'faqs': verified_data.get('faqs', []),
                'click_count': 0,
                'slug': verified_data.get('slug') or verified_data.get('company_name', 'unknown').lower().replace(' ', '-').replace('_', '-'),
                'detail_url': verified_data.get('detail_url', ''),
                'is_featured': False,
                'is_verified': False,
                'crawl_source': verified_data.get('crawl_source', 'producthunt_comprehensive'),
                'extraction_success': True,
                'data_quality_score': verified_data.get('data_quality_score', 85)
            }

            # تطبيق القيم المطلوبة
            for field, default_value in required_fields.items():
                if not verified_data.get(field) or (isinstance(verified_data[field], list) and len(verified_data[field]) == 0):
                    verified_data[field] = default_value

            # تحويل القوائم إلى تنسيق JSON إذا لزم الأمر
            if isinstance(verified_data['applicable_tasks'], list):
                verified_data['applicable_tasks'] = verified_data['applicable_tasks']
            if isinstance(verified_data['pros'], list):
                verified_data['pros'] = verified_data['pros']
            if isinstance(verified_data['cons'], list):
                verified_data['cons'] = verified_data['cons']

            # حساب درجة الجودة النهائية
            quality_score = 50  # البداية

            if len(verified_data['short_description']) > 50:
                quality_score += 10
            if len(verified_data['full_description']) > 100:
                quality_score += 10
            if len(verified_data['pros']) >= 3:
                quality_score += 10
            if len(verified_data['cons']) >= 2:
                quality_score += 5
            if verified_data['pricing'] != 'Contact for pricing':
                quality_score += 5
            if verified_data['logo_url']:
                quality_score += 5
            if verified_data['visit_website_url']:
                quality_score += 5

            verified_data['data_quality_score'] = min(quality_score, 100)
            verified_data['crawl_source'] = 'producthunt_comprehensive_verified'

            # إضافة معلومات التحقق
            verified_data['verification_timestamp'] = time.strftime('%Y-%m-%d %H:%M:%S')
            verified_data['all_fields_completed'] = True

            logger.info(f"✅ اكتمل التحقق النهائي: جودة {verified_data['data_quality_score']}%، جميع الحقول مكتملة")
            return verified_data

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق النهائي: {e}")
            # إرجاع البيانات الأصلية مع حد أدنى من المعلومات
            return {
                **data,
                'data_quality_score': 70,
                'extraction_success': True,
                'verification_error': str(e)
            }

    def convert_producthunt_to_tool_data(self, post: Dict) -> Dict:
        """Convert ProductHunt post to tool data format"""
        topics = [edge['node']['name'] for edge in post.get('topics', {}).get('edges', [])]
        tool_name = post.get('name', '')

        return {
            'company_name': tool_name,
            'short_description': post.get('tagline', ''),
            'full_description': post.get('description', ''),
            'visit_website_url': post.get('website', ''),
            'detail_url': post.get('url', ''),
            'logo_url': post.get('thumbnail', {}).get('url', ''),
            'slug': tool_name.lower().replace(' ', '-').replace('_', '-') if tool_name else 'unknown',  # Add slug
            'primary_task': topics[0] if topics else 'General',
            'applicable_tasks': json.dumps(topics),  # Convert to JSON string for SQLite
            'crawl_source': 'producthunt',
            'extraction_success': True,
            'data_quality_score': 85  # High quality from ProductHunt
        }

    def clean_tool_name(self, name: str) -> str:
        """Clean and normalize tool name"""
        # Remove common suffixes and prefixes
        name = re.sub(r'\s*-\s*(AI Tool|Tool|App|Software|Platform).*$', '', name, flags=re.IGNORECASE)
        name = re.sub(r'^(AI\s+)?Tool:\s*', '', name, flags=re.IGNORECASE)
        return name.strip()

    def extract_pricing_context(self, text: str, keyword: str) -> str:
        """Extract pricing context around keyword"""
        # Find sentences containing the keyword
        sentences = text.split('.')
        for sentence in sentences:
            if keyword in sentence.lower():
                return sentence.strip()[:200]  # Return first 200 chars
        return ""

    def is_verified_tool(self, _content_lower: str) -> bool:
        """Check if tool is verified - Always returns False as requested"""
        return False

    def create_basic_tool_data_from_page(self, soup, url: str, title: str) -> Optional[Dict]:
        """Create basic tool data from page content when normal extraction fails"""
        try:
            logger.info("🔧 Creating basic tool data from page content...")

            # Extract tool name from title
            tool_name = title.replace(' - AI Tool For Videos', '').replace(' - There\'s An AI For that®', '').strip()
            if not tool_name:
                tool_name = "Unknown Tool"

            # Try to find description
            description = ""

            # Look for meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and meta_desc.get('content'):
                description = meta_desc['content']

            # Look for og:description
            if not description:
                og_desc = soup.find('meta', attrs={'property': 'og:description'})
                if og_desc and og_desc.get('content'):
                    description = og_desc['content']

            # Look for first paragraph
            if not description:
                first_p = soup.find('p')
                if first_p:
                    description = first_p.get_text(strip=True)[:500]

            # Default description if nothing found
            if not description:
                description = f"{tool_name} - AI tool for video generation and content creation"

            # Try to find logo
            logo_url = ""
            logo_selectors = [
                'img[alt*="logo"]', 'img[class*="logo"]',
                '.logo img', '#logo img', 'header img'
            ]
            for selector in logo_selectors:
                logo = soup.select_one(selector)
                if logo and logo.get('src'):
                    logo_url = urljoin(url, logo['src'])
                    break

            # Create basic tool data
            tool_data = {
                'company_name': tool_name,
                'short_description': description[:500],
                'full_description': description,
                'visit_website_url': url,
                'detail_url': url,
                'logo_url': logo_url,
                'primary_task': 'Video Generation',
                'applicable_tasks': json.dumps(['Video Generation', 'AI Content Creation', 'Media Production']),
                'pricing': 'Unknown',
                'crawl_source': 'theresanaiforthat_basic',
                'extraction_success': True,
                'data_quality_score': 75,  # Medium quality for basic extraction
                'is_featured': False,
                'is_verified': False
            }

            logger.info(f"✅ Created basic tool data: {tool_name}")
            return tool_data

        except Exception as e:
            logger.error(f"❌ Error creating basic tool data: {e}")
            return None

    # ==========================================
    # LLM ENHANCEMENT SYSTEM
    # ==========================================

    def enhance_tool_data_with_llm(self, tool_data: Dict) -> Dict:
        """Enhance tool data using LLM"""
        if not self.enable_llm_enhancement:
            return tool_data

        logger.info(f"🤖 Enhancing data for: {tool_data.get('company_name', 'Unknown')}")

        # Fields that need enhancement
        enhancement_fields = {
            'pros': os.getenv('ENHANCE_PROS', 'true').lower() == 'true',
            'cons': os.getenv('ENHANCE_CONS', 'true').lower() == 'true',
            'faqs': os.getenv('ENHANCE_FAQS', 'true').lower() == 'true',
            'applicable_tasks': os.getenv('ENHANCE_CATEGORIES', 'true').lower() == 'true'
        }

        for field, should_enhance in enhancement_fields.items():
            if should_enhance and (not tool_data.get(field) or len(str(tool_data.get(field, ''))) == 0):
                enhanced_value = self.llm_enhance_field(tool_data, field)
                if enhanced_value:
                    tool_data[field] = enhanced_value
                    logger.info(f"✅ Enhanced {field} using LLM")

        return tool_data

    def llm_enhance_field(self, tool_data: Dict, field: str) -> Optional[any]:
        """Use LLM to enhance specific field"""
        try:
            # Create prompt based on field type
            prompt = self.create_enhancement_prompt(tool_data, field)

            # Try primary LLM first
            result = self.call_llm(self.primary_llm, prompt)

            # Try fallback LLMs if primary fails
            if not result and len(self.fallback_llms) > 0:
                for fallback_llm in self.fallback_llms:
                    result = self.call_llm(fallback_llm, prompt)
                    if result:
                        break

            if result:
                return self.parse_llm_response(result, field)

        except Exception as e:
            logger.error(f"❌ LLM enhancement error for {field}: {e}")

        return None

    def create_enhancement_prompt(self, tool_data: Dict, field: str) -> str:
        """Create LLM prompt for field enhancement"""
        tool_name = tool_data.get('company_name', 'Unknown Tool')
        description = tool_data.get('short_description', '') or tool_data.get('full_description', '')

        prompts = {
            'pros': f"""
Based on this AI tool information:
Tool Name: {tool_name}
Description: {description}

Generate 3-5 key advantages/benefits of this tool. Return as JSON array of strings.
Example: ["Easy to use", "Fast processing", "Great accuracy"]
""",
            'cons': f"""
Based on this AI tool information:
Tool Name: {tool_name}
Description: {description}

Generate 3-5 potential limitations/disadvantages of this tool. Return as JSON array of strings.
Example: ["Limited free tier", "Learning curve", "Requires internet connection"]
""",
            'faqs': f"""
Based on this AI tool information:
Tool Name: {tool_name}
Description: {description}

Generate 3-5 frequently asked questions about this tool. Return as JSON array of objects.
Example: [{{"question": "Is it free?", "answer": "It has a free tier with limitations"}}, ...]
""",
            'applicable_tasks': f"""
Based on this AI tool information:
Tool Name: {tool_name}
Description: {description}

Generate 3-7 specific tasks/categories this tool can be used for. Return as JSON array of strings.
Example: ["Content Writing", "SEO Optimization", "Social Media"]
"""
        }

        return prompts.get(field, '')

    def call_llm(self, llm_provider: str, prompt: str) -> Optional[str]:
        """Call specific LLM provider"""
        api_key = self.llm_keys.get(llm_provider)
        if not api_key:
            logger.warning(f"⚠️ No API key for {llm_provider}")
            return None

        try:
            if llm_provider == 'groq':
                return self.call_groq(prompt, api_key)
            elif llm_provider == 'deepseek':
                return self.call_deepseek(prompt, api_key)
            elif llm_provider == 'qwen':
                return self.call_qwen(prompt, api_key)
            elif llm_provider == 'mistral':
                return self.call_mistral(prompt, api_key)
            elif llm_provider == 'gemini':
                return self.call_google(prompt, api_key)
            elif llm_provider == 'openai':
                return self.call_openai(prompt, api_key)
            elif llm_provider == 'anthropic':
                return self.call_anthropic(prompt, api_key)
            elif llm_provider == 'google':
                return self.call_google(prompt, api_key)
            elif llm_provider == 'cohere':
                return self.call_cohere(prompt, api_key)
            elif llm_provider == 'together':
                return self.call_together(prompt, api_key)
            elif llm_provider == 'perplexity':
                return self.call_perplexity(prompt, api_key)
            elif llm_provider == 'ollama':
                return self.call_ollama(prompt, api_key)

        except Exception as e:
            logger.error(f"❌ Error calling {llm_provider}: {e}")

        return None

    def call_groq(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Groq API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'messages': [{'role': 'user', 'content': prompt}],
                'model': os.getenv('GROQ_MODEL', 'llama-3.1-70b-versatile'),
                'max_tokens': int(os.getenv('GROQ_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                'https://api.groq.com/openai/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ Groq API error: {e}")

        return None

    def call_openai(self, prompt: str, api_key: str) -> Optional[str]:
        """Call OpenAI API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'messages': [{'role': 'user', 'content': prompt}],
                'model': os.getenv('OPENAI_MODEL', 'gpt-4o-mini'),
                'max_tokens': int(os.getenv('OPENAI_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ OpenAI API error: {e}")

        return None

    def call_anthropic(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Anthropic Claude API"""
        try:
            headers = {
                'x-api-key': api_key,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            }

            data = {
                'model': os.getenv('ANTHROPIC_MODEL', 'claude-3-haiku-20240307'),
                'max_tokens': int(os.getenv('ANTHROPIC_MAX_TOKENS', '4000')),
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': 0.3
            }

            response = requests.post(
                'https://api.anthropic.com/v1/messages',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['content'][0]['text']

        except Exception as e:
            logger.error(f"❌ Anthropic API error: {e}")

        return None

    def call_google(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Google Gemini API"""
        try:
            model = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')
            url = f'https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}'

            data = {
                'contents': [{'parts': [{'text': prompt}]}],
                'generationConfig': {
                    'maxOutputTokens': int(os.getenv('GEMINI_MAX_TOKENS', '4000')),
                    'temperature': 0.3
                }
            }

            response = requests.post(url, json=data, timeout=self.llm_timeout)

            if response.status_code == 200:
                result = response.json()
                return result['candidates'][0]['content']['parts'][0]['text']

        except Exception as e:
            logger.error(f"❌ Google API error: {e}")

        return None

    def call_cohere(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Cohere API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': os.getenv('COHERE_MODEL', 'command-r-plus'),
                'message': prompt,
                'max_tokens': int(os.getenv('COHERE_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                'https://api.cohere.ai/v1/chat',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['text']

        except Exception as e:
            logger.error(f"❌ Cohere API error: {e}")

        return None

    def call_deepseek(self, prompt: str, api_key: str) -> Optional[str]:
        """Call DeepSeek API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'messages': [{'role': 'user', 'content': prompt}],
                'model': os.getenv('DEEPSEEK_MODEL', 'deepseek-chat'),
                'max_tokens': int(os.getenv('DEEPSEEK_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com') + '/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ DeepSeek API error: {e}")

        return None

    def call_qwen(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Qwen API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': os.getenv('QWEN_MODEL', 'qwen-turbo'),
                'input': {'messages': [{'role': 'user', 'content': prompt}]},
                'parameters': {
                    'max_tokens': int(os.getenv('QWEN_MAX_TOKENS', '4000')),
                    'temperature': 0.3
                }
            }

            response = requests.post(
                os.getenv('QWEN_BASE_URL', 'https://dashscope.aliyuncs.com/api/v1') + '/services/aigc/text-generation/generation',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['output']['text']

        except Exception as e:
            logger.error(f"❌ Qwen API error: {e}")

        return None

    def call_mistral(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Mistral API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': os.getenv('MISTRAL_MODEL', 'mistral-small-latest'),
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': int(os.getenv('MISTRAL_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                os.getenv('MISTRAL_BASE_URL', 'https://api.mistral.ai') + '/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ Mistral API error: {e}")

        return None

    def call_together(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Together AI API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': os.getenv('TOGETHER_MODEL', 'meta-llama/Llama-2-70b-chat-hf'),
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': int(os.getenv('TOGETHER_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                os.getenv('TOGETHER_BASE_URL', 'https://api.together.xyz') + '/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ Together API error: {e}")

        return None

    def call_perplexity(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Perplexity API"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': os.getenv('PERPLEXITY_MODEL', 'llama-3.1-sonar-small-128k-online'),
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': int(os.getenv('PERPLEXITY_MAX_TOKENS', '4000')),
                'temperature': 0.3
            }

            response = requests.post(
                os.getenv('PERPLEXITY_BASE_URL', 'https://api.perplexity.ai') + '/chat/completions',
                headers=headers,
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"❌ Perplexity API error: {e}")

        return None

    def call_gemini(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Google Gemini API"""
        try:
            if not api_key:
                return None

            import requests

            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"

            headers = {
                'Content-Type': 'application/json'
            }

            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0].get('content', {})
                    if 'parts' in content and len(content['parts']) > 0:
                        return content['parts'][0].get('text', '').strip()
            else:
                logger.error(f"❌ Gemini API error: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"❌ Gemini API error: {e}")

        return None

    def call_ollama(self, prompt: str, api_key: str) -> Optional[str]:
        """Call Ollama API (Local)"""
        try:
            base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

            data = {
                'model': os.getenv('OLLAMA_MODEL', 'llama3.1:8b'),
                'prompt': prompt,
                'stream': False
            }

            response = requests.post(
                f'{base_url}/api/generate',
                json=data,
                timeout=self.llm_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')

        except Exception as e:
            logger.error(f"❌ Ollama API error: {e}")

        return None

    def parse_llm_response(self, response: str, field: str) -> Optional[any]:
        """Parse LLM response based on field type"""
        try:
            # Clean response
            response = response.strip()

            # Try to extract JSON from response
            json_match = re.search(r'\[.*\]|\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)

                if field in ['pros', 'cons', 'applicable_tasks']:
                    if isinstance(parsed, list):
                        return json.dumps(parsed)  # Convert to JSON string for SQLite
                    return json.dumps([])
                elif field == 'faqs':
                    return json.dumps(parsed) if isinstance(parsed, (list, dict)) else json.dumps({})

            # Fallback: try to parse as simple list
            if field in ['pros', 'cons', 'applicable_tasks']:
                lines = [line.strip('- ').strip() for line in response.split('\n') if line.strip()]
                items = [line for line in lines if len(line) > 5][:5]  # Max 5 items
                return json.dumps(items)

        except Exception as e:
            logger.error(f"❌ Error parsing LLM response for {field}: {e}")

        return None

    # ==========================================
    # BATCH SEARCH FUNCTIONALITY
    # ==========================================

    def batch_search_tools(self, tool_list: List[Dict]) -> List[Dict]:
        """
        🔄 Batch search multiple tools using intelligent search

        Args:
            tool_list: List of dicts with 'name' and optional 'url' keys

        Returns:
            List of successfully processed tools
        """
        logger.info(f"🔄 Starting batch intelligent search for {len(tool_list)} tools")

        results = []
        for i, tool_info in enumerate(tool_list, 1):
            tool_name = tool_info.get('name', '')
            tool_url = tool_info.get('url', '')

            if not tool_name:
                logger.warning(f"⚠️ Skipping tool {i}: No name provided")
                continue

            logger.info(f"🔍 Processing {i}/{len(tool_list)}: {tool_name}")

            try:
                result = self.search_tool_intelligent(tool_name, tool_url)
                if result:
                    # Check for duplicates
                    if self.enable_duplicate_detection:
                        if self.is_duplicate_tool(result):
                            logger.info(f"🔄 Duplicate detected, skipping: {tool_name}")
                            continue

                    # Save to database
                    success = self.save_enhanced_tool_data(result)
                    if success:
                        results.append(result)
                        self.success_count += 1
                        logger.info(f"✅ Successfully processed: {tool_name}")
                    else:
                        self.error_count += 1
                        logger.error(f"❌ Failed to save: {tool_name}")
                else:
                    logger.warning(f"❌ Tool not found: {tool_name}")
                    self.error_count += 1

                # Rate limiting
                delay = int(os.getenv('LLM_REQUEST_DELAY', '1'))
                time.sleep(delay)

            except Exception as e:
                logger.error(f"❌ Error processing {tool_name}: {e}")
                self.error_count += 1
                continue

        logger.info(f"✅ Batch search completed: {len(results)}/{len(tool_list)} successful")
        return results

    def is_duplicate_tool(self, tool_data: Dict) -> bool:
        """Check if tool already exists in Supabase database"""
        if not self.enable_duplicate_detection:
            return False

        try:
            # Connect to Supabase PostgreSQL
            if not POSTGRES_AVAILABLE:
                logger.warning("⚠️ PostgreSQL not available, skipping duplicate check")
                return False

            conn = psycopg2.connect(**self.supabase_config)
            cursor = conn.cursor()

            # Check by name similarity
            tool_name = tool_data.get('company_name', '').lower()
            cursor.execute("""
                SELECT company_name, visit_website_url
                FROM tools
                WHERE LOWER(company_name) ILIKE %s
                LIMIT 5
            """, (f"%{tool_name}%",))

            existing_tools = cursor.fetchall()
            conn.close()

            for existing_name, existing_url in existing_tools:
                # Calculate similarity
                similarity = self.calculate_similarity(tool_name, existing_name.lower())

                # Check URL similarity if available
                tool_url = tool_data.get('visit_website_url', '')
                if tool_url and existing_url:
                    url_similarity = self.calculate_url_similarity(tool_url, existing_url)
                    if url_similarity > 0.8:  # High URL similarity
                        return True

                # Check name similarity
                if similarity > self.duplicate_threshold:
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ Error checking duplicates: {e}")
            return False

    def calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings"""
        try:
            # Simple word matching approach
            words1 = set(str1.lower().split())
            words2 = set(str2.lower().split())

            if not words1 or not words2:
                return 0.0

            intersection = words1.intersection(words2)
            union = words1.union(words2)

            return len(intersection) / len(union) if union else 0.0
        except:
            return 0.0

    def calculate_url_similarity(self, url1: str, url2: str) -> float:
        """Calculate similarity between two URLs"""
        try:
            parsed1 = urlparse(url1.lower())
            parsed2 = urlparse(url2.lower())

            # Compare domains
            domain1 = parsed1.netloc.replace('www.', '')
            domain2 = parsed2.netloc.replace('www.', '')

            if domain1 == domain2:
                return 1.0

            # Calculate domain similarity
            return self.calculate_similarity(domain1, domain2)

        except:
            return 0.0

    def save_enhanced_tool_data(self, tool_data: Dict) -> bool:
        """Save tool data with enhanced validation"""
        try:
            # Validate required fields
            required_fields = ['company_name']
            for field in required_fields:
                if not tool_data.get(field):
                    logger.warning(f"⚠️ Missing required field {field}, skipping save")
                    return False

            # Enhance data quality score based on completeness
            quality_score = self.calculate_data_quality_score(tool_data)
            tool_data['data_quality_score'] = quality_score

            # Set enhanced metadata
            tool_data['crawl_batch_id'] = self.batch_id
            tool_data['extraction_success'] = True
            tool_data['validation_notes'] = f"Enhanced search - Quality: {quality_score}%"

            # Use existing save method
            return self.save_to_database(tool_data)

        except Exception as e:
            logger.error(f"❌ Error saving enhanced tool data: {e}")
            return False

    def is_data_complete(self, tool_data: Dict) -> bool:
        """Check if tool data has all required fields (improved validation)"""
        try:
            missing_fields = []

            for field in self.required_fields:
                value = tool_data.get(field)

                # Check if field is missing, None, empty string, or just whitespace
                if not value or (isinstance(value, str) and not value.strip()):
                    missing_fields.append(field)

            if missing_fields:
                logger.info(f"📋 INCOMPLETE: {tool_data.get('company_name', 'Unknown')} - Missing: {', '.join(missing_fields)}")
                return False

            # Additional validation for URLs
            logo_url = tool_data.get('logo_url', '').strip()
            website_url = tool_data.get('visit_website_url', '').strip()

            if logo_url and not (logo_url.startswith('http') or logo_url.startswith('//')):
                logger.info(f"📋 INCOMPLETE: {tool_data.get('company_name', 'Unknown')} - Invalid logo URL format: '{logo_url[:50]}...'")
                return False

            if website_url and not (website_url.startswith('http') or website_url.startswith('//')):
                logger.info(f"📋 INCOMPLETE: {tool_data.get('company_name', 'Unknown')} - Invalid website URL format: '{website_url[:50]}...'")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ Error checking data completeness: {e}")
            return False

    def is_blocked_content(self, tool_data: Dict) -> bool:
        """Check if tool contains blocked content based on categories (improved specificity)"""
        try:
            # Get text fields
            company_name = tool_data.get('company_name', '').lower()
            short_desc = tool_data.get('short_description', '').lower()
            full_desc = tool_data.get('full_description', '').lower()
            primary_task = tool_data.get('primary_task', '').lower()

            # Check applicable tasks
            applicable_tasks = tool_data.get('applicable_tasks', [])
            if isinstance(applicable_tasks, str):
                applicable_tasks = [applicable_tasks]
            applicable_tasks_text = ' '.join(str(task).lower() for task in applicable_tasks)

            # Check primary task first (exact matches)
            for blocked_task in self.blocked_primary_tasks:
                if primary_task == blocked_task:
                    logger.info(f"🚫 BLOCKED: {tool_data.get('company_name', 'Unknown')} - Primary task is '{blocked_task}'")
                    return True

            # Check against blocked categories (specific phrases)
            for blocked_category in self.blocked_categories:
                if (blocked_category in company_name or
                    blocked_category in short_desc or
                    blocked_category in full_desc or
                    blocked_category in primary_task or
                    blocked_category in applicable_tasks_text):
                    logger.info(f"🚫 BLOCKED: {tool_data.get('company_name', 'Unknown')} - Contains '{blocked_category}'")
                    return True

            # Check dangerous single words (but with some exceptions)
            combined_text = f"{company_name} {short_desc} {full_desc} {primary_task} {applicable_tasks_text}"
            for dangerous_word in self.dangerous_single_words:
                if dangerous_word in combined_text:
                    # Allow some exceptions for legitimate uses
                    if dangerous_word == 'love' and ('love music' in combined_text or 'love songs' in combined_text):
                        continue
                    if dangerous_word == 'romantic' and ('romantic music' in combined_text or 'romantic songs' in combined_text):
                        continue
                    logger.info(f"🚫 BLOCKED: {tool_data.get('company_name', 'Unknown')} - Contains dangerous word '{dangerous_word}'")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ Error checking blocked content: {e}")
            return False

    def calculate_data_quality_score(self, tool_data: Dict) -> int:
        """Calculate data quality score based on field completeness"""
        total_fields = 0
        completed_fields = 0

        # Core fields (higher weight)
        core_fields = {
            'company_name': 3,
            'short_description': 2,
            'visit_website_url': 2,
            'full_description': 2
        }

        # Optional fields (lower weight)
        optional_fields = {
            'logo_url': 1,
            'primary_task': 1,
            'applicable_tasks': 1,
            'pros': 1,
            'cons': 1,
            'pricing': 1,
            'faqs': 1,
            'featured_image_url': 1
        }

        # Calculate core fields score
        for field, weight in core_fields.items():
            total_fields += weight
            if tool_data.get(field):
                completed_fields += weight

        # Calculate optional fields score
        for field, weight in optional_fields.items():
            total_fields += weight
            value = tool_data.get(field)
            if value and (isinstance(value, str) and len(value) > 0) or \
               (isinstance(value, list) and len(value) > 0):
                completed_fields += weight

        # Calculate percentage
        if total_fields == 0:
            return 0

        score = int((completed_fields / total_fields) * 100)
        return min(100, max(0, score))  # Ensure 0-100 range

    def run_intelligent_search_mode(self):
        """
        🔍 Run intelligent search mode for a single tool
        """
        logger.info("🔍 Starting Intelligent Search Mode")

        print("\n🔍 INTELLIGENT SEARCH MODE")
        print("=" * 60)
        print("Search for an AI tool using multiple sources with LLM enhancement")
        print()

        # Get tool name
        tool_name = input("Enter tool name: ").strip()
        if not tool_name:
            print("❌ No tool name provided")
            return []

        # Get tool URL (optional)
        tool_url = input("Enter tool URL (optional, press Enter to skip): ").strip()
        if not tool_url:
            tool_url = None

        print(f"\n🔍 Searching for: {tool_name}")
        if tool_url:
            print(f"🌐 URL: {tool_url}")

        # Setup browser for TheresAnAIForThat search
        browser_setup = False
        try:
            if self.setup_browser():
                browser_setup = True
                logger.info("✅ Browser setup for search")
            else:
                logger.warning("⚠️ Browser setup failed, will skip TheresAnAIForThat search")
        except Exception as e:
            logger.warning(f"⚠️ Browser setup error: {e}")

        # Search for the tool
        result = self.search_tool_intelligent(tool_name, tool_url)

        if result:
            # Check for duplicates
            if self.enable_duplicate_detection:
                if self.is_duplicate_tool(result):
                    print(f"🔄 Duplicate detected, updating existing tool: {tool_name}")
                    # Continue to save (will update existing)

            # Save to database
            success = self.save_enhanced_tool_data(result)
            if success:
                self.success_count += 1
                print(f"\n✅ Successfully processed: {tool_name}")
                print(f"📊 Tool: {result.get('company_name', 'Unknown')}")
                print(f"📊 Source: {result.get('crawl_source', 'unknown')}")
                print(f"📊 Quality: {result.get('data_quality_score', 0)}%")

                # Show enhanced data
                if result.get('pros'):
                    print(f"✅ Pros: {len(result.get('pros', '[]'))} items")
                if result.get('cons'):
                    print(f"⚠️ Cons: {len(result.get('cons', '[]'))} items")
                if result.get('faqs'):
                    print(f"❓ FAQs: Enhanced")

                return [result]
            else:
                self.error_count += 1
                print(f"❌ Failed to save: {tool_name}")
                return []
        else:
            self.error_count += 1
            print(f"❌ Tool not found: {tool_name}")
            print("\n💡 Suggestions:")
            print("   - Check the tool name spelling")
            print("   - Try providing the tool URL")
            print("   - Configure search APIs for better coverage")

        # Cleanup browser (handled automatically by cleanup_resources)
        logger.info("🧹 Browser cleanup will be handled automatically")

        return [] if not result else [result]

    def calculate_quality_score(self, tool_data: Dict) -> int:
        """Calculate data quality score (0-100)"""
        score = 0

        # Company name (20 points)
        if tool_data.get('company_name') and len(tool_data['company_name'].strip()) >= 2:
            score += 20

        # Description (15 points)
        if tool_data.get('short_description') and len(tool_data['short_description'].strip()) >= 20:
            score += 15

        # Website URL (15 points)
        if tool_data.get('visit_website_url') and tool_data['visit_website_url'].startswith('http'):
            score += 15

        # Logo URL (10 points)
        if tool_data.get('logo_url'):
            score += 10

        # Featured image URL (10 points)
        if tool_data.get('featured_image_url'):
            score += 10

        # Primary task (10 points)
        if tool_data.get('primary_task') and len(tool_data['primary_task'].strip()) >= 3:
            score += 10

        # Pricing (10 points)
        if tool_data.get('pricing'):
            score += 10

        # Slug (5 points)
        if tool_data.get('slug') and len(tool_data['slug'].strip()) >= 3:
            score += 5

        # Applicable tasks (5 points)
        if tool_data.get('applicable_tasks'):
            score += 5

        return min(score, 100)

    def save_to_database(self, tool_data: Dict) -> bool:
        """Save tool data to PostgreSQL (Supabase) - SIMPLIFIED VERSION"""
        try:
            logger.info(f"💾 VPS SAVE: Starting save for {tool_data.get('company_name', 'Unknown')}")

            # Check for blocked content first
            if self.is_blocked_content(tool_data):
                logger.info(f"🚫 SKIPPED: {tool_data.get('company_name', 'Unknown')} - Blocked content")
                self.blocked_count += 1
                return False

            # Check for data completeness
            if not self.is_data_complete(tool_data):
                logger.info(f"📋 SKIPPED: {tool_data.get('company_name', 'Unknown')} - Incomplete data")
                self.incomplete_count += 1
                return False

            # Validate database configuration
            if not self.database_config:
                logger.error("❌ VPS SAVE: No database configuration!")
                return False

            # Connect to PostgreSQL with simple connection
            import psycopg2
            from datetime import datetime

            logger.info(f"🔗 VPS SAVE: Connecting to database...")
            conn = psycopg2.connect(
                host=self.database_config['host'],
                port=self.database_config['port'],
                database=self.database_config['database'],
                user=self.database_config['user'],
                password=self.database_config['password'],
                connect_timeout=30
            )
            cursor = conn.cursor()
            logger.info(f"✅ VPS SAVE: Connected successfully")

            # Prepare basic data (simplified)
            company_name = tool_data.get('company_name', 'Unknown')
            slug = tool_data.get('slug', '')
            short_desc = tool_data.get('short_description', '')
            full_desc = tool_data.get('full_description', '')
            primary_task = tool_data.get('primary_task', '')
            pricing = tool_data.get('pricing', '')
            logo_url = tool_data.get('logo_url', '')
            featured_image_url = tool_data.get('featured_image_url', '')  # ✅ FIXED: Added featured_image_url
            website_url = tool_data.get('visit_website_url', '')

            logger.info(f"📝 VPS SAVE: Prepared data for {company_name} ({slug})")
            logger.info(f"🖼️ VPS SAVE: Featured image URL: {featured_image_url}")  # ✅ FIXED: Log featured image

            # Simple INSERT with basic fields only
            logger.info(f"💾 VPS SAVE: Executing INSERT...")
            cursor.execute("""
                INSERT INTO tools (
                    company_name, slug, short_description, full_description,
                    primary_task, pricing, logo_url, featured_image_url, visit_website_url,
                    created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (slug) DO UPDATE SET
                    company_name = EXCLUDED.company_name,
                    short_description = EXCLUDED.short_description,
                    full_description = EXCLUDED.full_description,
                    primary_task = EXCLUDED.primary_task,
                    pricing = EXCLUDED.pricing,
                    logo_url = EXCLUDED.logo_url,
                    featured_image_url = EXCLUDED.featured_image_url,
                    visit_website_url = EXCLUDED.visit_website_url,
                    updated_at = EXCLUDED.updated_at
                RETURNING id, company_name, slug
            """, (
                company_name, slug, short_desc, full_desc,
                primary_task, pricing, logo_url, featured_image_url, website_url,
                datetime.now(), datetime.now()
            ))

            result = cursor.fetchone()
            logger.info(f"📊 VPS SAVE: INSERT result: {result}")

            # CRITICAL: Commit the transaction
            logger.info(f"💾 VPS SAVE: Committing transaction...")
            conn.commit()
            logger.info(f"✅ VPS SAVE: Transaction committed successfully")

            # Verify the save
            logger.info(f"🔍 VPS SAVE: Verifying save...")
            cursor.execute('SELECT id, company_name, slug FROM tools WHERE slug = %s', (slug,))
            verification = cursor.fetchone()

            if verification:
                logger.info(f"✅ VPS SAVE: Verification successful - ID: {verification[0]}, Name: {verification[1]}")
                conn.close()
                return True
            else:
                logger.error(f"❌ VPS SAVE: Verification failed - record not found after insert")
                conn.close()
                return False

        except psycopg2.Error as e:
            logger.error(f"❌ VPS SAVE: PostgreSQL error: {e}")
            try:
                conn.close()
            except:
                pass
            return False
        except Exception as e:
            logger.error(f"❌ VPS SAVE: Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            try:
                conn.close()
            except:
                pass
            return False

    def create_backup(self, tool_count: int):
        """Create periodic backup of extracted data (disabled to prevent unwanted files)"""
        # Backup creation disabled to prevent file clutter
        logger.info(f"💾 Backup skipped for {tool_count} tools (file creation disabled)")
        return

    def save_to_backup_file(self, tool_data: Dict):
        """Save failed tool data to backup file (disabled to prevent unwanted files)"""
        # Failed tool backup disabled to prevent file clutter
        tool_name = tool_data.get('company_name', 'Unknown Tool')
        logger.info(f"💾 Failed tool backup skipped for {tool_name} (file creation disabled)")
        return

    def save_progress(self):
        """Save current progress to file for resume functionality (disabled to prevent unwanted files)"""
        # Progress saving disabled to prevent file clutter
        logger.debug(f"💾 Progress tracking: {len(self.processed_tools)} tools processed (file saving disabled)")
        return

    def load_progress(self):
        """Load previous progress for resume functionality (disabled to prevent unwanted files)"""
        # Progress loading disabled since progress saving is disabled
        logger.info("📂 Progress loading disabled, starting fresh session")
        return False

    def get_resume_info(self):
        """Get information about resumable sessions"""
        # Resume functionality disabled since progress saving is disabled
        logger.info("📂 Resume functionality disabled - no progress files to load")
        return []

    def handle_resume_session(self):
        """Handle resuming from previous session (disabled to prevent unwanted files)"""
        print("📂 Resume functionality disabled - starting fresh session")
        print("💡 Tip: All data is saved directly to database, no need for resume files")
        return False

    def run_crawl(self, max_tools: int = 10):
        """Run crawl session"""
        logger.info(f"🚀 Starting crawl for {max_tools} tools")

        if not self.setup_browser():
            logger.error("❌ Browser setup failed")
            return

        try:
            tools_to_crawl = self.ai_tools_list[:max_tools]

            for i, slug in enumerate(tools_to_crawl, 1):
                url = f"{self.base_url}/ai/{slug}/"
                logger.info(f"📊 Processing {i}/{len(tools_to_crawl)}: {slug}")

                # Extract data for this tool with 5 retries
                tool_data = self.bypass_and_extract_with_retries(url, max_retries=5)

                if tool_data:
                    # ✅ SAVE IMMEDIATELY after successful extraction
                    logger.info(f"💾 Saving {tool_data['company_name']} to database...")
                    save_result = self.save_to_database(tool_data)
                    if save_result:
                        self.extracted_tools.append(tool_data)
                        logger.info(f"✅ Successfully saved {tool_data['company_name']} to Supabase!")
                    else:
                        logger.error(f"❌ FAILED to save {tool_data['company_name']} to database!")
                        self.error_count += 1
                else:
                    # This should never happen with 5 retries, but log it
                    logger.error(f"❌ CRITICAL: Failed to extract {slug} after 5 attempts!")
                    self.error_count += 1

                # ⏱️ MANDATORY DELAY between tools (15-25 seconds)
                delay = random.uniform(15, 25)
                logger.info(f"⏱️ Waiting {delay:.1f}s before next tool...")
                time.sleep(delay)

                # Clear any browser issues before next tool
                try:
                    self.driver.refresh()
                    time.sleep(2)
                except:
                    pass

                # Progress report after each tool
                success_rate = (self.success_count / i * 100) if i > 0 else 0
                logger.info(f"📈 Progress: {i}/{len(tools_to_crawl)} - Success rate: {success_rate:.1f}%")
                logger.info(f"📊 Database records so far: {self.success_count}")

                # Check database after each save
                if tool_data:
                    try:
                        conn = sqlite3.connect('ai_tools_exact.db')
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM tools")
                        count = cursor.fetchone()[0]
                        conn.close()
                        logger.info(f"🗄️ Total records in database: {count}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not check database: {e}")

            # Export results
            self.export_results()

        finally:
            # Cleanup handled automatically by cleanup_resources
            logger.info("🧹 Crawl completed, cleanup will be handled automatically")

    def export_results(self):
        """Export results to JSON (disabled to prevent unwanted files)"""
        # Export disabled to prevent file clutter
        logger.info(f"📁 Results export skipped: {len(self.extracted_tools)} tools extracted (file creation disabled)")

        # Display summary instead
        if self.extracted_tools:
            logger.info("📊 Sample extracted tools:")
            for tool in self.extracted_tools[:5]:
                logger.info(f"  ✅ {tool.get('company_name', 'Unknown')} - {tool.get('primary_task', 'Unknown task')}")
            if len(self.extracted_tools) > 5:
                logger.info(f"  ... and {len(self.extracted_tools) - 5} more tools")
        return

    def log_final_summary(self):
        """Log final summary"""
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0

        safe_print("\n" + "=" * 60)
        safe_print("🔥 CORRECTED EXACT CRAWLER RESULTS")
        safe_print("=" * 60)
        safe_print(f"📊 Total processed: {total}")
        safe_print(f"✅ Successful: {self.success_count}")
        safe_print(f"❌ Failed: {self.error_count}")
        safe_print(f"🚫 Blocked (content filter): {self.blocked_count}")
        safe_print(f"📋 Incomplete (missing data): {self.incomplete_count}")
        safe_print(f"🎯 Success rate: {success_rate:.1f}%")
        safe_print(f"🗄️ Database: Supabase PostgreSQL (direct save)")
        safe_print(f"📁 File creation: Disabled (clean workspace)")

        if self.extracted_tools:
            safe_print(f"\n🎉 Sample extracted tools:")
            for tool in self.extracted_tools[:5]:
                safe_print(f"  ✅ {tool['company_name']} ({tool['primary_task']})")
                safe_print(f"     Website: {tool['visit_website_url']}")
                safe_print(f"     Pricing: {tool['pricing']}")
                safe_print(f"     Quality: {tool['data_quality_score']}/100")

        safe_print("=" * 60)

    def run_comprehensive_crawl(self, discovery_mode: bool = True):
        """Run comprehensive crawl of all AI tools"""
        logger.info("🚀 Starting COMPREHENSIVE AI Tools Crawl")
        logger.info("=" * 60)

        start_time = time.time()

        # Install dependencies first
        if not self.install_dependencies():
            logger.error("❌ Failed to install dependencies")
            return False

        # Setup browser
        if not self.setup_browser():
            logger.error("❌ Failed to setup browser")
            return False

        try:
            # PROGRESSIVE DISCOVERY AND EXTRACTION (Period by Period)
            if discovery_mode:
                logger.info("🔄 PROGRESSIVE DISCOVERY AND EXTRACTION")
                logger.info("From OLDEST to NEWEST periods - systematic foundation-first coverage")
                logger.info("=" * 60)

                self.run_ordered_extraction()
            else:
                logger.info("⚠️ Discovery mode disabled, using predefined tool list")

                # Phase 2: Data Extraction for predefined tools
                logger.info("🔍 PHASE 2: Data Extraction")
                logger.info("=" * 60)

                # Extract all tools
                for i, tool_slug in enumerate(self.ai_tools_list, 1):
                    logger.info(f"\n{'='*60}")
                    logger.info(f"🔍 Processing tool {i}/{len(self.ai_tools_list)}: {tool_slug}")
                    logger.info(f"{'='*60}")

                    url = f"{self.base_url}/ai/{tool_slug}/"

                    # Extract with retries
                    tool_data = self.bypass_and_extract_with_retries(url)

                    if tool_data:
                        # Save to database immediately
                        if self.save_to_database(tool_data):
                            logger.info(f"✅ Tool {tool_slug} saved successfully")
                            self.extracted_tools.append(tool_data)
                        else:
                            logger.error(f"❌ Failed to save {tool_slug} to database")
                            self.error_count += 1
                    else:
                        logger.error(f"❌ Failed to extract data for {tool_slug}")
                        self.error_count += 1

                    # Delay between tools
                    logger.info("⏱️ Waiting 10 seconds before next tool...")
                    time.sleep(10)

            # Final summary
            duration = time.time() - start_time
            logger.info(f"\n{'='*60}")
            logger.info(f"🎉 CRAWL COMPLETED!")
            logger.info(f"✅ Success: {self.success_count}")
            logger.info(f"❌ Errors: {self.error_count}")
            logger.info(f"⏱️ Duration: {duration:.2f} seconds")
            logger.info(f"📊 Success Rate: {(self.success_count/(self.success_count+self.error_count)*100):.1f}%")
            logger.info(f"{'='*60}")

            return True

        except KeyboardInterrupt:
            logger.info("\n⚠️ Crawl interrupted by user")
            return False
        except Exception as e:
            logger.error(f"❌ Crawl failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # Cleanup handled automatically by cleanup_resources
            logger.info("🧹 Comprehensive crawl completed, cleanup will be handled automatically")

    def run_periodic_update(self, focus_on_recent: bool = True):
        """Run periodic update focusing on recent tools"""
        logger.info("🔄 Starting PERIODIC UPDATE")
        logger.info("=" * 60)

        if focus_on_recent:
            # Focus on recent months only - ordered from NEWEST to OLDEST for priority coverage
            recent_urls = [
                "https://theresanaiforthat.com/period/just-released/",
                "https://theresanaiforthat.com/period/december/",
                "https://theresanaiforthat.com/period/november/",
                "https://theresanaiforthat.com/period/october/",
                "https://theresanaiforthat.com/period/september/",
                "https://theresanaiforthat.com/period/august/",
                "https://theresanaiforthat.com/period/july/",
                "https://theresanaiforthat.com/period/june/",
                "https://theresanaiforthat.com/period/may/",
                "https://theresanaiforthat.com/period/april/",
                "https://theresanaiforthat.com/period/march/",
                "https://theresanaiforthat.com/period/february/",
                "https://theresanaiforthat.com/period/january/"
            ]
            self.period_urls = recent_urls
            logger.info(f"🎯 Focusing on {len(recent_urls)} recent periods (newest to oldest)")

        return self.run_comprehensive_crawl(discovery_mode=True)

    def run_ordered_extraction(self):
        """Run ordered extraction from OLDEST to NEWEST periods"""
        logger.info("🔄 Starting ORDERED EXTRACTION")
        logger.info("📅 Processing from OLDEST to NEWEST periods - systematic coverage")
        logger.info("🎯 Each period processed completely before moving to next")
        logger.info("=" * 80)

        # Setup browser if not already done
        if not hasattr(self, 'driver') or self.driver is None:
            logger.info("🌐 Setting up browser for ordered extraction...")
            if not self.setup_browser():
                logger.error("❌ Failed to setup browser")
                return False

        total_extracted = 0

        # Process each period in order (OLDEST to NEWEST)
        for period_index, period_url in enumerate(self.period_urls):
            logger.info(f"\n{'🔥'*80}")
            logger.info(f"📅 PERIOD {period_index + 1}/{len(self.period_urls)}: {period_url}")
            logger.info(f"{'🔥'*80}")

            try:
                # STEP 1: Discover all tools from this period
                logger.info(f"🔍 STEP 1: Discovering tools from {period_url}")
                period_tools = self.discover_tools_from_single_period(period_url, max_pages=999)

                if not period_tools:
                    logger.warning(f"⚠️ No tools found in period {period_url}, skipping")
                    continue

                logger.info(f"✅ Found {len(period_tools)} tools in this period")

                # STEP 2: Extract and save each tool immediately
                logger.info(f"🔍 STEP 2: Extracting and saving {len(period_tools)} tools")
                logger.info("=" * 60)
                logger.info(f"🎯 VPS MODE: Starting extraction with enhanced logging...")
                logger.info(f"📋 Tools to extract: {period_tools[:5]}...")  # Show first 5 tools

                period_success = 0
                period_errors = 0

                for tool_index, tool_slug in enumerate(period_tools, 1):
                    logger.info(f"\n{'='*60}")
                    logger.info(f"🔍 Tool {tool_index}/{len(period_tools)} in Period {period_index + 1}: {tool_slug}")
                    logger.info(f"{'='*60}")

                    url = f"{self.base_url}/ai/{tool_slug}/"

                    # Extract with retries
                    tool_data = self.bypass_and_extract_with_retries(url)

                    if tool_data:
                        # Debug extracted data
                        logger.info(f"🔍 EXTRACTED: {tool_data.get('company_name')}")
                        logger.info(f"🔍 Data types: tasks={type(tool_data.get('applicable_tasks'))}, pros={type(tool_data.get('pros'))}, cons={type(tool_data.get('cons'))}")

                        # Save to database immediately
                        logger.info(f"💾 SAVING {tool_slug} to Supabase...")
                        save_result = self.save_to_database(tool_data)

                        if save_result:
                            logger.info(f"✅ SUCCESS: {tool_slug} saved to Supabase")
                            period_success += 1
                            total_extracted += 1
                        else:
                            logger.error(f"❌ FAILED: {tool_slug} could not be saved")
                            period_errors += 1
                    else:
                        logger.error(f"❌ EXTRACTION FAILED: {tool_slug}")
                        period_errors += 1

                    # Delay between tools
                    import random
                    delay = random.uniform(10, 20)
                    logger.info(f"⏱️ Waiting {delay:.1f}s before next tool...")
                    time.sleep(delay)

                logger.info(f"📊 Period {period_index + 1} completed: {period_success} saved, {period_errors} failed")

            except Exception as e:
                logger.error(f"❌ Error processing period {period_url}: {e}")
                continue

        logger.info(f"🎉 ORDERED EXTRACTION COMPLETED: {total_extracted} tools extracted and saved")

    def run_progressive_discovery_and_extraction(self, max_pages_per_period: int = 999, max_tools_per_period: int = None):
        """Run progressive discovery and extraction - OLDEST to NEWEST periods for comprehensive coverage"""
        logger.info("🔄 Starting PROGRESSIVE DISCOVERY AND EXTRACTION")
        logger.info("📅 Processing from OLDEST to NEWEST periods - building complete foundation first")
        logger.info(f"⚙️ Settings: {max_pages_per_period} pages per period, NO TOOL LIMITS (extract ALL tools from ALL pages)")
        logger.info("📊 Expected scale: 200,000+ tools across 23 periods with deep pagination")
        logger.info("⏱️ Estimated time: Several hours for complete extraction")
        logger.info("=" * 80)

        # Setup browser if not already done
        if not hasattr(self, 'driver') or self.driver is None:
            if not self.setup_browser():
                logger.error("❌ Failed to setup browser")
                return

        total_extracted = 0

        # Start from the last processed period if resuming
        start_period_index = max(0, self.current_period_index)

        # Log resume information
        if start_period_index > 0:
            logger.info(f"🔄 RESUMING CRAWL from period {start_period_index + 1}")
            logger.info(f"📊 Already processed {len(self.processed_tools)} tools")
            logger.info(f"⏭️ Skipping first {start_period_index} completed periods")

        for period_index in range(start_period_index, len(self.period_urls)):
            period_url = self.period_urls[period_index]
            logger.info(f"\n{'🔥'*80}")
            logger.info(f"📅 PERIOD {period_index + 1}/{len(self.period_urls)}: {period_url}")
            if start_period_index > 0 and period_index == start_period_index:
                logger.info(f"🔄 RESUMING from this period (skipped {start_period_index} completed periods)")
            logger.info(f"{'🔥'*80}")

            # Update current period index for progress tracking
            self.current_period_index = period_index

            try:
                # STEP 1: Discover tools from this period (with timeout)
                logger.info(f"🔍 STEP 1: Discovering tools from {period_url}")

                # Discovery (Windows compatible)
                period_tools = self.discover_tools_from_single_period(period_url, max_pages_per_period)

                if not period_tools:
                    logger.warning(f"⚠️ No tools found in period {period_url}, skipping")
                    continue

                # Filter out already processed tools and limit per period
                new_tools = [tool for tool in period_tools if tool not in self.processed_tools]

                if len(new_tools) != len(period_tools):
                    skipped_count = len(period_tools) - len(new_tools)
                    logger.info(f"🔄 Found {len(period_tools)} tools, {skipped_count} already processed")
                    logger.info(f"📊 Resume: Continuing with {len(new_tools)} new tools")
                else:
                    logger.info(f"✅ Found {len(period_tools)} new tools in this period")

                if max_tools_per_period and len(new_tools) > max_tools_per_period:
                    logger.info(f"⚙️ Limiting to first {max_tools_per_period} tools (found {len(new_tools)})")
                    period_tools = new_tools[:max_tools_per_period]
                else:
                    period_tools = new_tools
                    if not max_tools_per_period:
                        logger.info(f"🚀 Processing ALL {len(new_tools)} tools from this period (no limits)")

                # Add to discovered tools set
                self.discovered_tools.update(period_tools)

                # Skip period if no new tools
                if not period_tools:
                    logger.info(f"✅ Period {period_index + 1} already completed, moving to next period")
                    # Update period index and save progress
                    self.current_period_index = period_index
                    self.save_progress()
                    continue

                logger.info(f"✅ Found {len(period_tools)} tools in this period")

                # STEP 2: Extract data for each tool immediately
                logger.info(f"🔍 STEP 2: Extracting data for {len(period_tools)} tools")
                logger.info("=" * 60)

                period_success = 0
                period_errors = 0
                period_start_time = time.time()  # Track period timing

                for tool_index, tool_slug in enumerate(period_tools, 1):
                    logger.info(f"\n{'='*60}")
                    logger.info(f"🔍 Tool {tool_index}/{len(period_tools)} in Period {period_index + 1}: {tool_slug}")
                    logger.info(f"{'='*60}")

                    url = f"{self.base_url}/ai/{tool_slug}/"

                    # Extract with retries
                    tool_data = self.bypass_and_extract_with_retries(url)

                    if tool_data:
                        # Debug extracted data before saving
                        logger.info(f"🔍 EXTRACTED DATA for {tool_slug}:")
                        logger.info(f"   Company: {tool_data.get('company_name')}")
                        logger.info(f"   Slug: {tool_data.get('slug')}")
                        logger.info(f"   Tasks type: {type(tool_data.get('applicable_tasks'))} = {tool_data.get('applicable_tasks')}")
                        logger.info(f"   Pros type: {type(tool_data.get('pros'))} = {tool_data.get('pros')}")
                        logger.info(f"   Cons type: {type(tool_data.get('cons'))} = {tool_data.get('cons')}")

                        # Save to database immediately
                        logger.info(f"💾 ATTEMPTING TO SAVE {tool_slug} to database...")
                        save_result = self.save_to_database(tool_data)
                        logger.info(f"💾 SAVE RESULT for {tool_slug}: {save_result}")

                        if save_result:
                            logger.info(f"✅ Tool {tool_slug} saved successfully to Supabase")
                            self.extracted_tools.append(tool_data)
                            self.processed_tools.add(tool_slug)  # Track processed tools
                            period_success += 1
                            total_extracted += 1
                            self.success_count += 1

                            # Save progress after each successful tool
                            self.save_progress()

                            # Create periodic backup every 10 tools
                            if total_extracted % 10 == 0:
                                self.create_backup(total_extracted)
                        else:
                            logger.error(f"❌ CRITICAL: Failed to save {tool_slug} to database!")
                            logger.error(f"❌ Tool data was extracted but save_to_database returned False")
                            # Log failed tool (backup disabled)
                            self.save_to_backup_file(tool_data)
                            self.processed_tools.add(tool_slug)  # Mark as processed even if failed
                            period_errors += 1
                            self.error_count += 1

                            # Save progress after each tool (success or failure)
                            self.save_progress()
                    else:
                        logger.error(f"❌ Failed to extract data for {tool_slug}")
                        period_errors += 1
                        self.error_count += 1

                    # Enhanced progress update with ETA and speed
                    elapsed_time = time.time() - period_start_time
                    if tool_index > 0 and elapsed_time > 0:
                        avg_time_per_tool = elapsed_time / tool_index
                        remaining_tools = len(period_tools) - tool_index
                        eta_seconds = remaining_tools * avg_time_per_tool
                        eta_minutes = eta_seconds / 60

                        logger.info(f"📊 Period Progress: {tool_index}/{len(period_tools)} - Success: {period_success}, Errors: {period_errors}")
                        logger.info(f"📊 Total Progress: {total_extracted} tools extracted so far")
                        logger.info(f"⏱️ Speed: {avg_time_per_tool:.1f}s/tool | ETA for period: {eta_minutes:.1f} minutes")
                    else:
                        logger.info(f"📊 Period Progress: {tool_index}/{len(period_tools)} - Success: {period_success}, Errors: {period_errors}")
                        logger.info(f"📊 Total Progress: {total_extracted} tools extracted so far")

                    # Smart delay between tools (adaptive based on success rate)
                    if period_success > period_errors:
                        delay = random.uniform(5, 8)  # Faster when successful
                    else:
                        delay = random.uniform(10, 15)  # Slower when having issues
                    logger.info(f"⏱️ Waiting {delay:.1f}s before next tool...")
                    time.sleep(delay)

                # Period summary
                period_rate = (period_success / len(period_tools) * 100) if len(period_tools) > 0 else 0
                logger.info(f"\n{'✅'*60}")
                logger.info(f"📊 PERIOD {period_index + 1} COMPLETED!")
                logger.info(f"✅ Success: {period_success}/{len(period_tools)} ({period_rate:.1f}%)")
                logger.info(f"❌ Errors: {period_errors}")
                logger.info(f"📊 Total extracted so far: {total_extracted}")
                logger.info(f"{'✅'*60}")

                # Smart delay between periods (based on period success rate)
                if period_rate > 80:
                    period_delay = random.uniform(15, 25)  # Faster when very successful
                elif period_rate > 50:
                    period_delay = random.uniform(25, 35)  # Normal when moderately successful
                else:
                    period_delay = random.uniform(45, 60)  # Slower when having issues
                logger.info(f"⏱️ Waiting {period_delay:.1f}s before next period...")
                time.sleep(period_delay)

            except Exception as e:
                logger.error(f"❌ Error processing period {period_url}: {e}")

                # Enhanced error handling with recovery attempts
                if "cloudflare" in str(e).lower() or "blocked" in str(e).lower():
                    logger.info("🔄 Cloudflare/blocking detected, waiting longer before retry...")
                    time.sleep(60)
                elif "connection" in str(e).lower() or "timeout" in str(e).lower():
                    logger.info("🔄 Connection issue detected, brief pause before continuing...")
                    time.sleep(30)
                else:
                    logger.info("🔄 General error, brief pause before continuing...")
                    time.sleep(15)

                # Update period index and save progress even on error
                self.current_period_index = period_index
                self.save_progress()

                import traceback
                traceback.print_exc()
                continue

        # Enhanced final summary with quality metrics
        logger.info(f"\n{'🎉'*80}")
        logger.info(f"🎉 PROGRESSIVE CRAWL COMPLETED!")
        logger.info(f"📊 Total tools extracted: {total_extracted}")
        logger.info(f"📊 Total errors: {self.error_count}")
        total_attempts = total_extracted + self.error_count
        if total_attempts > 0:
            success_rate = (total_extracted / total_attempts) * 100
            logger.info(f"📊 Success rate: {success_rate:.1f}%")

            # Quality metrics
            if self.extracted_tools:
                avg_quality = sum(tool.get('data_quality_score', 0) for tool in self.extracted_tools) / len(self.extracted_tools)
                high_quality_tools = sum(1 for tool in self.extracted_tools if tool.get('data_quality_score', 0) >= 90)
                logger.info(f"📊 Average quality score: {avg_quality:.1f}/100")
                logger.info(f"📊 High quality tools (90+): {high_quality_tools}/{total_extracted} ({high_quality_tools/total_extracted*100:.1f}%)")
        else:
            logger.info(f"📊 Success rate: 0.0% (no tools processed)")
        logger.info(f"{'🎉'*80}")

    def run_period_range_crawl(self, start_year: int, end_year: int):
        """Run crawl for specific period range using EXACT same method as option 1"""
        logger.info(f"📅 PERIOD RANGE CRAWL: {start_year}-{end_year}")
        logger.info("=" * 80)

        # Filter period URLs for the specified range
        filtered_periods = []
        for period_url in self.period_urls:
            # Extract year from URL (e.g., /period/2023/ -> 2023)
            year_match = re.search(r'/period/(\d{4})/', period_url)
            if year_match:
                year = int(year_match.group(1))
                if start_year <= year <= end_year:
                    filtered_periods.append(period_url)

        if not filtered_periods:
            logger.warning(f"⚠️ No periods found for range {start_year}-{end_year}")
            return

        logger.info(f"🎯 Found {len(filtered_periods)} periods in range {start_year}-{end_year}:")
        for period_url in filtered_periods:
            year_match = re.search(r'/period/(\d{4})/', period_url)
            year = year_match.group(1) if year_match else "Unknown"
            logger.info(f"   📅 {year}: {period_url}")

        # Install dependencies first (SAME AS OPTION 1)
        if not self.install_dependencies():
            logger.error("❌ Failed to install dependencies")
            return False

        # Setup browser (SAME AS OPTION 1)
        if not self.setup_browser():
            logger.error("❌ Failed to setup browser")
            return False

        total_extracted = 0

        # Process each period in the range (USING EXACT SAME LOGIC AS OPTION 1)
        for period_index, period_url in enumerate(filtered_periods):
            year_match = re.search(r'/period/(\d{4})/', period_url)
            year = year_match.group(1) if year_match else "Unknown"

            logger.info(f"\n{'🔥'*80}")
            logger.info(f"📅 PERIOD {period_index + 1}/{len(filtered_periods)}: {period_url}")
            logger.info(f"{'🔥'*80}")

            try:
                # STEP 1: Discover all tools from this period (SAME AS OPTION 1)
                logger.info(f"🔍 STEP 1: Discovering tools from {period_url}")
                period_tools = self.discover_tools_from_single_period(period_url, max_pages=999)

                if not period_tools:
                    logger.warning(f"⚠️ No tools found in period {period_url}, skipping")
                    continue

                logger.info(f"✅ Found {len(period_tools)} tools in this period")

                # STEP 2: Extract and save each tool immediately (EXACT SAME AS OPTION 1)
                logger.info(f"🔍 STEP 2: Extracting and saving {len(period_tools)} tools")
                logger.info("=" * 60)
                logger.info(f"🎯 VPS MODE: Starting extraction with enhanced logging...")
                logger.info(f"📋 Tools to extract: {period_tools[:5]}...")  # Show first 5 tools

                period_success = 0
                period_errors = 0

                for tool_index, tool_slug in enumerate(period_tools, 1):
                    logger.info(f"\n{'='*60}")
                    logger.info(f"🔍 Tool {tool_index}/{len(period_tools)} in Period {period_index + 1}: {tool_slug}")
                    logger.info(f"{'='*60}")

                    url = f"{self.base_url}/ai/{tool_slug}/"

                    # Extract with retries (EXACT SAME AS OPTION 1)
                    tool_data = self.bypass_and_extract_with_retries(url)

                    if tool_data:
                        # Debug extracted data (EXACT SAME AS OPTION 1)
                        logger.info(f"🔍 EXTRACTED: {tool_data.get('company_name')}")
                        logger.info(f"🔍 Data types: tasks={type(tool_data.get('applicable_tasks'))}, pros={type(tool_data.get('pros'))}, cons={type(tool_data.get('cons'))}")

                        # Save to database immediately (EXACT SAME AS OPTION 1)
                        logger.info(f"💾 SAVING {tool_slug} to Supabase...")
                        save_result = self.save_to_database(tool_data)

                        if save_result:
                            logger.info(f"✅ SUCCESS: {tool_slug} saved to Supabase")
                            period_success += 1
                            total_extracted += 1
                        else:
                            logger.error(f"❌ FAILED: {tool_slug} could not be saved")
                            period_errors += 1
                    else:
                        logger.error(f"❌ EXTRACTION FAILED: {tool_slug}")
                        period_errors += 1

                    # Delay between tools (EXACT SAME AS OPTION 1)
                    import random
                    delay = random.uniform(10, 20)
                    logger.info(f"⏱️ Waiting {delay:.1f}s before next tool...")
                    time.sleep(delay)

                logger.info(f"📊 Period {period_index + 1} completed: {period_success} saved, {period_errors} failed")

            except Exception as e:
                logger.error(f"❌ Error processing period {period_url}: {e}")
                continue

        logger.info(f"🎉 ORDERED EXTRACTION COMPLETED: {total_extracted} tools extracted and saved")

    def discover_tools_from_single_period(self, period_url: str, max_pages: int = 999) -> List[str]:
        """Discover tools from a single period with structure analysis first - no tool limits"""
        logger.info(f"🔍 Discovering tools from: {period_url}")

        all_tools = set()

        try:
            # STEP 1: Navigate and analyze page structure
            logger.info(f"🌐 Navigating to: {period_url}")
            self.driver.get(period_url)
            time.sleep(10)

            # Smart Cloudflare detection for period discovery
            page_title = self.driver.title
            logger.info(f"📄 Page title: {page_title}")

            # Use smart detection
            is_cloudflare, confidence, reason = smart_cloudflare_detection(self.driver, logger)

            if is_cloudflare and confidence == "high":
                logger.info(f"🛡️ Cloudflare detected during discovery: {reason}")
                try:
                    # Use threading to implement timeout for bypass
                    import threading

                    bypass_completed = False
                    bypass_error = None

                    def run_bypass():
                        nonlocal bypass_completed, bypass_error
                        try:
                            self.cf_bypasser.bypass()
                            bypass_completed = True
                        except Exception as e:
                            bypass_error = e

                    # Start bypass in thread with timeout
                    bypass_thread = threading.Thread(target=run_bypass)
                    bypass_thread.daemon = True
                    bypass_thread.start()

                    # Wait for bypass with 90 second timeout (increased for difficult periods)
                    bypass_thread.join(timeout=90)

                    if bypass_completed:
                        logger.info("✅ Cloudflare bypass completed")
                        time.sleep(10)  # Wait longer for page to fully load
                    elif bypass_error:
                        logger.warning(f"⚠️ Bypass failed with error: {bypass_error}")
                        # Try one more time with longer wait
                        logger.info("🔄 Attempting second bypass...")
                        time.sleep(30)
                        try:
                            self.cf_bypasser.bypass()
                            logger.info("✅ Second bypass attempt successful")
                            time.sleep(10)
                        except:
                            logger.warning("⚠️ Second bypass also failed, skipping period")
                            return []
                    else:
                        logger.warning("⚠️ Bypass timed out after 90 seconds")
                        # Try one more time with longer wait
                        logger.info("🔄 Attempting second bypass after timeout...")
                        time.sleep(30)
                        try:
                            self.cf_bypasser.bypass()
                            logger.info("✅ Second bypass attempt successful")
                            time.sleep(10)
                        except:
                            logger.warning("⚠️ Second bypass also failed, skipping period")
                            return []

                except Exception as e:
                    logger.warning(f"⚠️ Bypass setup failed: {e}")
                    time.sleep(15)
                    return []  # Return empty list to skip this period
            elif is_cloudflare and confidence == "low":
                logger.info(f"⚠️ Low confidence Cloudflare detection, skipping bypass: {reason}")
                time.sleep(5)
            else:
                logger.info(f"✅ No Cloudflare protection detected during discovery: {reason}")

            # Handle login popup
            self.handle_login_popup()

            # STEP 2: Verify bypass success and analyze page structure
            final_title = self.driver.title
            logger.info(f"📄 Final page title: {final_title}")

            # Final smart check for discovery
            is_still_cloudflare, final_confidence, final_reason = smart_cloudflare_detection(self.driver, logger)

            if is_still_cloudflare and final_confidence == "high":
                logger.warning(f"⚠️ Still on Cloudflare page after bypass attempts: {final_reason}")
                return []
            elif is_still_cloudflare and final_confidence == "low":
                logger.info(f"⚠️ Weak Cloudflare indicators but continuing with discovery: {final_reason}")
            else:
                logger.info(f"✅ Page ready for tool discovery: {final_reason}")

            html_content = self.driver.html
            logger.info(f"📄 HTML length: {len(html_content)} characters")

            if len(html_content) < 5000:
                logger.warning(f"⚠️ Content too short, may be blocked")
                return []

            # Check for Cloudflare content in HTML
            if "just a moment" in html_content.lower() or "ray id" in html_content.lower():
                logger.warning("⚠️ HTML still contains Cloudflare content")
                return []

            # STEP 3: Analyze structure and extract tools
            logger.info("🔍 Analyzing page structure...")
            page_analysis = self.analyze_page_structure(html_content)

            if not page_analysis['tools_found']:
                logger.warning("⚠️ No tools found in structure analysis")
                return []

            logger.info(f"✅ Structure analysis: {page_analysis['total_tools']} tools, {page_analysis['container_pattern']}")
            all_tools.update(page_analysis['tools_found'])

            # STEP 4: Check additional pages if needed
            if max_pages > 1:
                logger.info(f"📄 Checking additional pages (max {max_pages})...")

                for page_num in range(2, max_pages + 1):  # Check all pages up to max_pages
                    try:
                        next_page_url = f"{period_url.rstrip('/')}/page/{page_num}/"
                        logger.info(f"📄 Page {page_num}: {next_page_url}")

                        self.driver.get(next_page_url)
                        time.sleep(5)
                        self.handle_login_popup()

                        # Check if page exists (404 or redirect)
                        current_url = self.driver.url
                        if f"/page/{page_num}/" not in current_url:
                            logger.info(f"📄 Page {page_num} redirected or not found, stopping pagination")
                            break

                        # Quick analysis of additional page
                        html_content = self.driver.html
                        if len(html_content) < 5000:
                            logger.info(f"📄 Page {page_num} empty, stopping")
                            break

                        page_analysis = self.analyze_page_structure(html_content)
                        if page_analysis['tools_found']:
                            all_tools.update(page_analysis['tools_found'])
                            logger.info(f"✅ Page {page_num}: +{len(page_analysis['tools_found'])} tools")
                        else:
                            logger.info(f"📄 Page {page_num}: no tools, stopping")
                            break

                        time.sleep(3)

                    except Exception as e:
                        logger.warning(f"⚠️ Error on page {page_num}: {e}")
                        break

        except Exception as e:
            logger.error(f"❌ Error processing period {period_url}: {e}")
            import traceback
            traceback.print_exc()
            return []

        discovered_list = list(all_tools)
        logger.info(f"🎉 Period discovery complete! Found {len(discovered_list)} unique tools")

        return discovered_list

    def analyze_page_structure(self, html_content: str) -> dict:
        """Analyze page structure to understand layout and extract tools"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            analysis = {
                'total_tools': 0,
                'tools_found': [],
                'container_pattern': 'unknown',
                'pagination_info': {}
            }

            # Find all AI tool links
            ai_links = soup.find_all('a', href=re.compile(r'/ai/[^/]+/?$'))
            logger.info(f"🔗 Found {len(ai_links)} AI tool links")

            # Extract tool slugs
            tools = []
            for link in ai_links:
                href = link.get('href', '')
                slug_match = re.search(r'/ai/([^/]+)/?$', href)
                if slug_match:
                    slug = slug_match.group(1)
                    if slug not in ['', 'index', 'home'] and slug not in tools:
                        tools.append(slug)

            analysis['total_tools'] = len(tools)
            analysis['tools_found'] = tools

            # Analyze container patterns
            if ai_links:
                # Find common parent containers
                container_classes = {}
                for link in ai_links[:10]:  # Sample first 10
                    parent = link.parent
                    level = 0
                    while parent and level < 3:
                        if parent.get('class'):
                            classes = ' '.join(parent.get('class'))
                            container_classes[classes] = container_classes.get(classes, 0) + 1
                        parent = parent.parent
                        level += 1

                if container_classes:
                    # Find most common pattern
                    best_pattern = max(container_classes.items(), key=lambda x: x[1])
                    analysis['container_pattern'] = best_pattern[0]

            # Analyze pagination
            pagination_links = soup.find_all('a', href=re.compile(r'\?page=\d+'))
            if pagination_links:
                page_numbers = []
                for link in pagination_links:
                    href = link.get('href', '')
                    page_match = re.search(r'page=(\d+)', href)
                    if page_match:
                        page_numbers.append(int(page_match.group(1)))

                if page_numbers:
                    analysis['pagination_info'] = {
                        'max_page': max(page_numbers),
                        'total_pagination_links': len(pagination_links)
                    }

            logger.info(f"📊 Analysis: {len(tools)} tools, pattern: {analysis['container_pattern'][:50]}...")
            return analysis

        except Exception as e:
            logger.error(f"❌ Structure analysis failed: {e}")
            return {
                'total_tools': 0,
                'tools_found': [],
                'container_pattern': 'error',
                'pagination_info': {}
            }

    def handle_login_popup(self):
        """Handle login popup that appears on theresanaiforthat.com"""
        try:
            # Wait a moment for popup to appear
            time.sleep(2)

            # Look for common popup close buttons
            close_selectors = [
                'button[aria-label="Close"]',
                '.close-button',
                '.modal-close',
                '[data-dismiss="modal"]',
                '.popup-close',
                'button:contains("×")',
                'button:contains("Close")',
                '.fa-times',
                '.fa-close'
            ]

            for selector in close_selectors:
                try:
                    close_button = self.driver.ele(selector, timeout=1)
                    if close_button:
                        close_button.click()
                        logger.info("✅ Closed login popup")
                        time.sleep(2)
                        return
                except:
                    continue

            # Try pressing Escape key
            try:
                self.driver.actions.key_down('Escape').key_up('Escape').perform()
                logger.info("✅ Pressed Escape to close popup")
                time.sleep(2)
            except:
                pass

        except Exception as e:
            logger.debug(f"🔍 No popup found or error handling popup: {e}")
            pass

def main():
    """Main function"""
    import argparse

    # Parse command line arguments for automation
    parser = argparse.ArgumentParser(description='AI Tools Crawler')
    parser.add_argument('--mode', choices=['auto', 'interactive'], default='interactive',
                       help='Run mode: auto (no user input) or interactive')
    parser.add_argument('--auto-choice', type=str, default='1',
                       help='Auto choice for non-interactive mode (1-5)')
    parser.add_argument('--crawl-mode', choices=['1', '2', '3', '4'], default='1',
                       help='Crawl mode: 1=Progressive, 2=Periodic, 3=Resume, 4=Intelligent')

    parser.add_argument('--full-crawl', action='store_true',
                       help='Perform full crawl (all available tools)')
    parser.add_argument('--no-limits', action='store_true',
                       help='No limits on number of tools (time-based crawling)')
    parser.add_argument('--session-id', type=str,
                       help='Session ID for tracking crawl sessions')
    parser.add_argument('--cycle', type=int, default=1,
                       help='Cycle number within a session')
    parser.add_argument('--quiet', action='store_true',
                       help='Suppress non-essential output')

    args = parser.parse_args()

    if not args.quiet:
        safe_print("🔥 COMPREHENSIVE AI TOOLS CRAWLER")
        safe_print("Perfect replica of PostgreSQL tools table structure")
        safe_print("Discovers and extracts ALL tools from theresanaiforthat.com")
        safe_print("")

    # Check for command line arguments (legacy support)
    import sys
    if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
        mode = sys.argv[1].lower()
        return run_automated_mode(mode)

    crawler = CorrectedExactCrawler()

    # Make crawler available globally for cleanup
    globals()['crawler'] = crawler

    if not crawler.install_dependencies():
        safe_print("❌ Failed to install dependencies")
        return

    # Auto mode - run without user interaction
    if args.mode == 'auto':
        if not args.quiet:
            safe_print("🤖 Running in AUTO mode")
            if args.no_limits:
                safe_print("🚀 No limits mode - time-based crawling")
                if args.session_id:
                    safe_print(f"📋 Session ID: {args.session_id}")
                    safe_print(f"🔄 Cycle: {args.cycle}")
            else:
                safe_print(f"🔄 Full crawl: {args.full_crawl}")

        # Determine crawl option based on parameters
        if args.no_limits:
            choice = '1'  # Progressive crawl (best for continuous operation)
        elif args.full_crawl:
            choice = '1'  # Progressive crawl
        else:
            choice = args.auto_choice  # Use specified auto choice

    else:
        # Check if running in Docker mode
        docker_mode = os.getenv('DOCKER_MODE', 'false').lower() == 'true'
        if docker_mode:
            # Auto mode for Docker
            choice = os.getenv('AUTO_CHOICE', '1')
            safe_print(f"🐳 Docker mode detected - Auto choice: {choice}")
        else:
            # Interactive mode
            safe_print("\n" + "="*70)
            safe_print("🤖 AI TOOLS CRAWLER - ENHANCED MENU")
            safe_print("="*70)
            safe_print("MAIN OPTIONS:")
            safe_print("1. 🔄 PROGRESSIVE CRAWL (Period by period: discover → extract)")
            safe_print("2. 🔄 PERIODIC UPDATE (Recent tools only)")
            safe_print("3. 📂 RESUME PREVIOUS SESSION (Continue from where you left off)")
            safe_print("4. 🔍 INTELLIGENT SEARCH (Multi-source search with LLM enhancement)")
            safe_print("")
            safe_print("📅 HISTORICAL PERIODS (Detailed Breakdown):")
            safe_print("5. 📅 EARLY ERA: 2015-2017 (Foundation Period)")
            safe_print("6. 📅 GROWTH: 2018-2019 (Development Period)")
            safe_print("7. 📅 TRANSITION: 2020 (Pandemic Impact)")
            safe_print("8. 📅 EXPANSION: 2021 (Recovery & Growth)")
            safe_print("9. 📅 ACCELERATION: 2022 (AI Advancement)")
            safe_print("10. 📅 BOOM H1: 2023 First Half (Jan-Jun)")
            safe_print("11. 📅 BOOM H2: 2023 Second Half (Jul-Dec)")
            safe_print("")
            safe_print("📅 2024 QUARTERLY BREAKDOWN:")
            safe_print("12. 📅 2024 Q1: January-March")
            safe_print("13. 📅 2024 Q2: April-June")
            safe_print("14. 📅 2024 Q3: July-September")
            safe_print("15. 📅 2024 Q4: October-December")
            safe_print("")
            safe_print("📅 2025 MONTHLY BREAKDOWN:")
            safe_print("16. 📅 2025 January")
            safe_print("17. 📅 2025 February")
            safe_print("18. 📅 2025 March")
            safe_print("19. 📅 2025 April")
            safe_print("20. 📅 2025 May")
            safe_print("21. 📅 2025 June")
            safe_print("22. 📅 2025 July")
            safe_print("23. 📅 2025 August")
            safe_print("24. 📅 2025 September")
            safe_print("25. 📅 2025 October")
            safe_print("26. 📅 2025 November")
            safe_print("27. 📅 2025 December")
            safe_print("")
            safe_print("28. 📅 FUTURE: 2026+ (Future Tools)")
            safe_print("0. ❌ EXIT")
            safe_print("="*70)

            choice = input("\nEnter your choice (0-28): ").strip()

    # Process the choice
    if choice == "1":
        print("🔄 Starting PROGRESSIVE CRAWL...")
        crawler.run_comprehensive_crawl(discovery_mode=True)
    elif choice == "2":
        print("🔄 Starting PERIODIC UPDATE...")
        crawler.run_periodic_update(focus_on_recent=True)
    elif choice == "3":
        crawler.handle_resume_session()
    elif choice == "4":
        print("🔍 Starting INTELLIGENT SEARCH...")
        if crawler.enable_intelligent_search:
            results = crawler.run_intelligent_search_mode()
            if results:
                print(f"✅ Intelligent search completed! Processed {len(results)} tools")
        else:
            print("⚠️ Intelligent search disabled")
    # Historical periods (detailed breakdown)
    elif choice == "5":
        print("📅 Starting EARLY ERA: 2015-2017...")
        crawler.run_period_range_crawl(2015, 2017)
    elif choice == "6":
        print("📅 Starting GROWTH PERIOD: 2018-2019...")
        crawler.run_period_range_crawl(2018, 2019)
    elif choice == "7":
        print("📅 Starting TRANSITION YEAR: 2020...")
        crawler.run_period_range_crawl(2020, 2020)
    elif choice == "8":
        print("📅 Starting EXPANSION YEAR: 2021...")
        crawler.run_period_range_crawl(2021, 2021)
    elif choice == "9":
        print("📅 Starting ACCELERATION YEAR: 2022...")
        crawler.run_period_range_crawl(2022, 2022)
    elif choice == "10":
        print("📅 Starting 2023 FIRST HALF (Jan-Jun)...")
        crawler.run_period_specific_crawl("2023", months_range=(1, 6))
    elif choice == "11":
        print("📅 Starting 2023 SECOND HALF (Jul-Dec)...")
        crawler.run_period_specific_crawl("2023", months_range=(7, 12))

    # 2024 quarterly breakdown
    elif choice == "12":
        print("📅 Starting 2024 Q1 (Jan-Mar)...")
        crawler.run_period_specific_crawl("2024", months_range=(1, 3))
    elif choice == "13":
        print("📅 Starting 2024 Q2 (Apr-Jun)...")
        crawler.run_period_specific_crawl("2024", months_range=(4, 6))
    elif choice == "14":
        print("📅 Starting 2024 Q3 (Jul-Sep)...")
        crawler.run_period_specific_crawl("2024", months_range=(7, 9))
    elif choice == "15":
        print("📅 Starting 2024 Q4 (Oct-Dec)...")
        crawler.run_period_specific_crawl("2024", months_range=(10, 12))

    # 2025 monthly breakdown
    elif choice == "16":
        print("📅 Starting 2025 JANUARY...")
        crawler.run_monthly_crawl("2025", 1)
    elif choice == "17":
        print("📅 Starting 2025 FEBRUARY...")
        crawler.run_monthly_crawl("2025", 2)
    elif choice == "18":
        print("📅 Starting 2025 MARCH...")
        crawler.run_monthly_crawl("2025", 3)
    elif choice == "19":
        print("📅 Starting 2025 APRIL...")
        crawler.run_monthly_crawl("2025", 4)
    elif choice == "20":
        print("📅 Starting 2025 MAY...")
        crawler.run_monthly_crawl("2025", 5)
    elif choice == "21":
        print("📅 Starting 2025 JUNE...")
        crawler.run_monthly_crawl("2025", 6)
    elif choice == "22":
        print("📅 Starting 2025 JULY...")
        crawler.run_monthly_crawl("2025", 7)
    elif choice == "23":
        print("📅 Starting 2025 AUGUST...")
        crawler.run_monthly_crawl("2025", 8)
    elif choice == "24":
        print("📅 Starting 2025 SEPTEMBER...")
        crawler.run_monthly_crawl("2025", 9)
    elif choice == "25":
        print("📅 Starting 2025 OCTOBER...")
        crawler.run_monthly_crawl("2025", 10)
    elif choice == "26":
        print("📅 Starting 2025 NOVEMBER...")
        crawler.run_monthly_crawl("2025", 11)
    elif choice == "27":
        print("📅 Starting 2025 DECEMBER...")
        crawler.run_monthly_crawl("2025", 12)

    # Future periods
    elif choice == "28":
        print("📅 Starting FUTURE PERIODS: 2026+...")
        crawler.run_period_range_crawl(2026, 2030)
    elif choice == "0":
        print("👋 Goodbye!")
        return
    else:
        print("❌ Invalid choice. Please try again.")
        return

    crawler.log_final_summary()
    crawler.cleanup_resources()

def run_automated_mode(mode):
    """Run crawler in automated mode without user input"""
    try:
        print(f"🤖 Running in automated mode: {mode}")
    except UnicodeEncodeError:
        print(f"Running in automated mode: {mode}")

    crawler = CorrectedExactCrawler()
    globals()['crawler'] = crawler

    if not crawler.install_dependencies():
        try:
            print("❌ Failed to install dependencies")
        except UnicodeEncodeError:
            print("Failed to install dependencies")
        return False

    try:
        if mode in ['progressive', '1']:
            try:
                print("🔄 Running progressive crawl")
            except UnicodeEncodeError:
                print("Running progressive crawl")
            crawler.run_comprehensive_crawl(discovery_mode=True)
        elif mode in ['periodic', '2']:
            try:
                print("🔄 Running periodic update")
            except UnicodeEncodeError:
                print("Running periodic update")
            crawler.run_periodic_update(focus_on_recent=True)
        elif mode in ['resume', '3']:
            try:
                print("📂 Resuming previous session")
            except UnicodeEncodeError:
                print("Resuming previous session")
            crawler.handle_resume_session()
        elif mode in ['intelligent', '4']:
            try:
                print("🔍 Running intelligent search")
            except UnicodeEncodeError:
                print("Running intelligent search")
            if crawler.enable_intelligent_search:
                results = crawler.run_intelligent_search_mode()
                if results:
                    try:
                        print(f"✅ Intelligent search completed! Processed {len(results)} tools")
                    except UnicodeEncodeError:
                        print(f"Intelligent search completed! Processed {len(results)} tools")
                else:
                    try:
                        print("❌ No tools found")
                    except UnicodeEncodeError:
                        print("No tools found")
            else:
                try:
                    print("⚠️ Intelligent search disabled in configuration")
                except UnicodeEncodeError:
                    print("Intelligent search disabled in configuration")
        else:
            try:
                print(f"❌ Unknown mode: {mode}")
                print("Available modes: progressive, periodic, resume, intelligent")
            except UnicodeEncodeError:
                print(f"Unknown mode: {mode}")
                print("Available modes: progressive, periodic, resume, intelligent")
            return False

        crawler.log_final_summary()
        return True

    except Exception as e:
        try:
            print(f"❌ Automated crawl failed: {e}")
        except UnicodeEncodeError:
            print(f"Automated crawl failed: {e}")
        return False
    finally:
        crawler.cleanup_resources()

if __name__ == "__main__":
    crawler = None
    try:
        main()
    except KeyboardInterrupt:
        try:
            print("\n❌ Crawl interrupted")
        except UnicodeEncodeError:
            print("\nCrawl interrupted")
        if 'crawler' in locals() and crawler:
            crawler.cleanup_resources()
    except Exception as e:
        try:
            print(f"❌ Crawl failed: {e}")
        except UnicodeEncodeError:
            print(f"Crawl failed: {e}")
        import traceback
        traceback.print_exc()
        if 'crawler' in locals() and crawler:
            crawler.cleanup_resources()
    finally:
        # Final cleanup attempt
        if 'crawler' in locals() and crawler:
            crawler.cleanup_resources()
        try:
            print("🧹 Final cleanup completed")
        except UnicodeEncodeError:
            print("Final cleanup completed")