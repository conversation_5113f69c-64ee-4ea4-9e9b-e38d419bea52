#!/usr/bin/env python3
"""
Database Cleaner Script
تنظيف قاعدة البيانات من الأدوات غير المكتملة والمحظورة
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import logging
from typing import Dict, List, Set
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_cleaner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def safe_print(message: str):
    """Safe print function that handles Unicode issues on Windows"""
    try:
        print(message)
    except UnicodeEncodeError:
        # Remove emojis and special characters for Windows compatibility
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(safe_message)

class DatabaseCleaner:
    """Database cleaner for removing incomplete and blocked content"""
    
    def __init__(self):
        self.database_config = self.setup_database_config()
        
        # Blocked categories - specific dating/romance phrases
        self.blocked_categories = {
            'virtual girlfriend', 'ex-girlfriend simulation', 'anime girlfriend ais',
            'dating advice', 'online dating chat suggestions', 'online dating assistance',
            'dating profile optimization', 'dating profile bios', 'dating simulator',
            'dating profile images', 'dating communication', 'dating conversations',
            'dating profiles', 'virtual dating rpg', 'alien dating profiles',
            'virtual dating game', 'anime dating simulation', 'mindful dating',
            'virtual dating ais', 'girlfriend ai', 'virtual girlfriend ai',
            'romance ai', 'romantic ai', 'flirt ai', 'dating ai',
            'virtual companion ai', 'ai girlfriend', 'ai boyfriend',
            'dating app', 'dating site', 'dating coach', 'dating tips',
            'love letter', 'love poem', 'love song', 'love story', 'love advice',
            'romance novel', 'romance story', 'romance game', 'romantic relationship',
            'relationship advice', 'relationship coach', 'relationship tips',
            'virtual companion for dating', 'ai companion for dating', 'dating companion',
            'girlfriend simulator', 'boyfriend simulator', 'romantic companion',
            'nsfw', 'adult content', 'erotic', 'sexual', 'porn', 'xxx',
            'hookup', 'flirting', 'seduction', 'sensual',
            'intimate relationship', 'intimate chat', 'intimate conversation',
            'virtual companion', 'ai companion', 'romantic companion'
        }

        # Single dangerous words that should be blocked in any context
        self.dangerous_single_words = {
            'girlfriend', 'boyfriend', 'dating', 'romance', 'romantic', 'love', 'flirt', 'flirting',
            'companionship'
        }

        # Primary task specific blocks
        self.blocked_primary_tasks = {
            'online dating assistance', 'online dating chat suggestions', 'dating advice',
            'companionship', 'virtual girlfriend', 'love letters', 'flirting',
            'dating assistance', 'love compatibility'
        }
        
        # Required fields for data quality
        self.required_fields = [
            'logo_url', 'company_name', 'short_description', 
            'full_description', 'primary_task', 'visit_website_url'
        ]
        
        # Counters
        self.blocked_deleted = 0
        self.incomplete_deleted = 0
        self.total_checked = 0

        # Detailed tracking
        self.blocked_tools = []  # List of (tool_name, reason)
        self.incomplete_tools = []  # List of (tool_name, reason)
        
    def setup_database_config(self) -> Dict:
        """Setup database configuration from environment"""
        try:
            config = {
                'host': os.getenv('SUPABASE_HOST'),
                'database': os.getenv('SUPABASE_DATABASE'),
                'user': os.getenv('SUPABASE_USER'),
                'password': os.getenv('SUPABASE_PASSWORD'),
                'port': int(os.getenv('SUPABASE_PORT', 5432))
            }
            
            # Validate configuration
            missing = [k for k, v in config.items() if not v]
            if missing:
                logger.error(f"Missing database config: {missing}")
                return None

            logger.info("Database configuration loaded")
            return config

        except Exception as e:
            logger.error(f"Database config error: {e}")
            return None
    
    def connect_to_database(self):
        """Connect to PostgreSQL database"""
        try:
            if not self.database_config:
                logger.error("No database configuration")
                return None

            conn = psycopg2.connect(**self.database_config)
            logger.info("Connected to Supabase PostgreSQL")
            return conn

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def is_blocked_content(self, tool_data: Dict) -> tuple:
        """Check if tool contains blocked content - returns (is_blocked, reason)"""
        try:
            # Get text fields
            company_name = tool_data.get('company_name', '').lower()
            short_desc = tool_data.get('short_description', '').lower()
            full_desc = tool_data.get('full_description', '').lower()
            primary_task = tool_data.get('primary_task', '').lower()

            # Check applicable tasks
            applicable_tasks = tool_data.get('applicable_tasks', [])
            if isinstance(applicable_tasks, str):
                try:
                    import json
                    applicable_tasks = json.loads(applicable_tasks)
                except:
                    applicable_tasks = [applicable_tasks]
            elif applicable_tasks is None:
                applicable_tasks = []

            applicable_tasks_text = ' '.join(str(task).lower() for task in applicable_tasks)

            # Check primary task first (exact matches)
            for blocked_task in self.blocked_primary_tasks:
                if primary_task == blocked_task:
                    return True, f"Primary task is '{blocked_task}'"

            # Check against blocked categories (specific phrases)
            for blocked_category in self.blocked_categories:
                if blocked_category in company_name:
                    return True, f"Company name contains '{blocked_category}'"
                elif blocked_category in short_desc:
                    return True, f"Short description contains '{blocked_category}'"
                elif blocked_category in full_desc:
                    return True, f"Full description contains '{blocked_category}'"
                elif blocked_category in primary_task:
                    return True, f"Primary task contains '{blocked_category}'"
                elif blocked_category in applicable_tasks_text:
                    return True, f"Applicable tasks contain '{blocked_category}'"

            # Check dangerous single words (but with some exceptions)
            combined_text = f"{company_name} {short_desc} {full_desc} {primary_task} {applicable_tasks_text}"
            for dangerous_word in self.dangerous_single_words:
                if dangerous_word in combined_text:
                    # Allow some exceptions for legitimate uses
                    if dangerous_word == 'love' and ('love music' in combined_text or 'love songs' in combined_text):
                        continue
                    if dangerous_word == 'romantic' and ('romantic music' in combined_text or 'romantic songs' in combined_text):
                        continue
                    return True, f"Contains dangerous word '{dangerous_word}'"

            return False, ""

        except Exception as e:
            logger.error(f"Error checking blocked content: {e}")
            return False, ""
    
    def is_data_incomplete(self, tool_data: Dict) -> tuple:
        """Check if tool data is incomplete - returns (is_incomplete, reason)"""
        try:
            missing_fields = []

            for field in self.required_fields:
                value = tool_data.get(field)

                # Check if field is missing, None, empty string, or just whitespace
                if not value or (isinstance(value, str) and not value.strip()):
                    missing_fields.append(field)

            if missing_fields:
                return True, f"Missing required fields: {', '.join(missing_fields)}"

            # Additional validation for URLs
            logo_url = tool_data.get('logo_url', '').strip()
            website_url = tool_data.get('visit_website_url', '').strip()

            if logo_url and not (logo_url.startswith('http') or logo_url.startswith('//')):
                return True, f"Invalid logo URL format: '{logo_url[:50]}...'"

            if website_url and not (website_url.startswith('http') or website_url.startswith('//')):
                return True, f"Invalid website URL format: '{website_url[:50]}...'"

            return False, ""

        except Exception as e:
            logger.error(f"Error checking data completeness: {e}")
            return True, "Error during validation"
    
    def get_all_tools(self, conn) -> List[Dict]:
        """Get all tools from database"""
        try:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            cursor.execute("""
                SELECT id, company_name, short_description, full_description,
                       primary_task, logo_url, visit_website_url, slug
                FROM tools
                ORDER BY id
            """)
            
            tools = cursor.fetchall()
            cursor.close()
            
            logger.info(f"Retrieved {len(tools)} tools from database")
            return [dict(tool) for tool in tools]

        except Exception as e:
            logger.error(f"Error retrieving tools: {e}")
            return []
    
    def delete_tool(self, conn, tool_id: int, reason: str) -> bool:
        """Delete a tool from database"""
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM tools WHERE id = %s", (tool_id,))
            conn.commit()
            cursor.close()
            
            logger.info(f"DELETED: Tool ID {tool_id} - {reason}")
            return True

        except Exception as e:
            logger.error(f"Error deleting tool {tool_id}: {e}")
            return False
    
    def clean_database(self, dry_run: bool = False):
        """Clean database by removing blocked and incomplete tools"""
        logger.info("Starting database cleanup...")
        logger.info(f"Mode: {'DRY RUN (no actual deletion)' if dry_run else 'LIVE CLEANUP'}")

        conn = self.connect_to_database()
        if not conn:
            logger.error("Cannot connect to database")
            return
        
        try:
            # Get all tools
            tools = self.get_all_tools(conn)
            self.total_checked = len(tools)
            
            if not tools:
                logger.warning("No tools found in database")
                return

            logger.info(f"Checking {len(tools)} tools...")

            # Check each tool
            for tool in tools:
                tool_id = tool['id']
                company_name = tool.get('company_name', 'Unknown')

                # Check if blocked content
                is_blocked, block_reason = self.is_blocked_content(tool)
                if is_blocked:
                    self.blocked_tools.append((company_name, block_reason))
                    if not dry_run:
                        if self.delete_tool(conn, tool_id, f"Blocked: {block_reason}"):
                            self.blocked_deleted += 1
                            logger.info(f"DELETED (blocked): {company_name} - {block_reason}")
                    else:
                        safe_print(f"BLOCKED: {company_name} - {block_reason}")
                        logger.info(f"WOULD DELETE (blocked): {company_name} - {block_reason}")
                        self.blocked_deleted += 1
                    continue

                # Check if incomplete data
                is_incomplete, incomplete_reason = self.is_data_incomplete(tool)
                if is_incomplete:
                    self.incomplete_tools.append((company_name, incomplete_reason))
                    if not dry_run:
                        if self.delete_tool(conn, tool_id, f"Incomplete: {incomplete_reason}"):
                            self.incomplete_deleted += 1
                            logger.info(f"DELETED (incomplete): {company_name} - {incomplete_reason}")
                    else:
                        safe_print(f"INCOMPLETE: {company_name} - {incomplete_reason}")
                        logger.info(f"WOULD DELETE (incomplete): {company_name} - {incomplete_reason}")
                        self.incomplete_deleted += 1
                    continue
            
            # Print summary
            self.print_summary(dry_run)
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
        finally:
            conn.close()
            logger.info("Database connection closed")
    
    def print_summary(self, dry_run: bool):
        """Print cleanup summary with detailed reasons"""
        total_to_delete = self.blocked_deleted + self.incomplete_deleted
        remaining = self.total_checked - total_to_delete

        safe_print("\n" + "=" * 80)
        safe_print("DATABASE CLEANUP SUMMARY")
        safe_print("=" * 80)
        safe_print(f"Total tools checked: {self.total_checked}")
        safe_print(f"Blocked content: {self.blocked_deleted}")
        safe_print(f"Incomplete data: {self.incomplete_deleted}")
        safe_print(f"Total to delete: {total_to_delete}")
        safe_print(f"Remaining clean tools: {remaining}")
        safe_print(f"Mode: {'DRY RUN' if dry_run else 'LIVE CLEANUP'}")

        # Show blocked tools details
        if self.blocked_tools:
            safe_print("\n" + "-" * 80)
            safe_print("BLOCKED TOOLS (Content Filter):")
            safe_print("-" * 80)
            for i, (tool_name, reason) in enumerate(self.blocked_tools[:20], 1):  # Show first 20
                safe_print(f"{i:2d}. {tool_name[:40]:<40} | {reason}")
            if len(self.blocked_tools) > 20:
                safe_print(f"... and {len(self.blocked_tools) - 20} more blocked tools")

        # Show incomplete tools details
        if self.incomplete_tools:
            safe_print("\n" + "-" * 80)
            safe_print("INCOMPLETE TOOLS (Missing Data):")
            safe_print("-" * 80)
            for i, (tool_name, reason) in enumerate(self.incomplete_tools[:20], 1):  # Show first 20
                safe_print(f"{i:2d}. {tool_name[:40]:<40} | {reason}")
            if len(self.incomplete_tools) > 20:
                safe_print(f"... and {len(self.incomplete_tools) - 20} more incomplete tools")

        safe_print("=" * 80)

        # Ask if user wants to save detailed report
        if dry_run and (self.blocked_tools or self.incomplete_tools):
            save_report = input("\nSave detailed report to file? (y/n): ").strip().lower()
            if save_report == 'y':
                self.save_detailed_report()

    def save_detailed_report(self):
        """Save detailed report to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"cleanup_report_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("AI TOOLS DATABASE CLEANUP REPORT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total tools checked: {self.total_checked}\n")
                f.write(f"Blocked content: {self.blocked_deleted}\n")
                f.write(f"Incomplete data: {self.incomplete_deleted}\n")
                f.write(f"Total to delete: {self.blocked_deleted + self.incomplete_deleted}\n\n")

                # Blocked tools details
                if self.blocked_tools:
                    f.write("BLOCKED TOOLS (Content Filter):\n")
                    f.write("-" * 80 + "\n")
                    for i, (tool_name, reason) in enumerate(self.blocked_tools, 1):
                        f.write(f"{i:3d}. {tool_name:<50} | {reason}\n")
                    f.write("\n")

                # Incomplete tools details
                if self.incomplete_tools:
                    f.write("INCOMPLETE TOOLS (Missing Data):\n")
                    f.write("-" * 80 + "\n")
                    for i, (tool_name, reason) in enumerate(self.incomplete_tools, 1):
                        f.write(f"{i:3d}. {tool_name:<50} | {reason}\n")
                    f.write("\n")

            safe_print(f"Detailed report saved to: {filename}")

        except Exception as e:
            logger.error(f"Error saving report: {e}")

def main():
    """Main function"""
    safe_print("AI Tools Database Cleaner")
    safe_print("=" * 40)

    cleaner = DatabaseCleaner()

    # Ask user for mode
    safe_print("Choose cleanup mode:")
    safe_print("1. DRY RUN (check only, no deletion)")
    safe_print("2. LIVE CLEANUP (actual deletion)")
    safe_print("3. Exit")

    choice = input("\nEnter your choice (1-3): ").strip()

    if choice == "1":
        safe_print("Running DRY RUN mode...")
        cleaner.clean_database(dry_run=True)
    elif choice == "2":
        confirm = input("WARNING: This will permanently delete tools! Type 'CONFIRM' to proceed: ")
        if confirm == "CONFIRM":
            safe_print("Running LIVE CLEANUP...")
            cleaner.clean_database(dry_run=False)
        else:
            safe_print("Cleanup cancelled")
    elif choice == "3":
        safe_print("Exiting")
    else:
        safe_print("Invalid choice")

if __name__ == "__main__":
    main()
